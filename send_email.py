"""
简单的 Outlook 365 邮件发送脚本
只支持发送文本邮件，所有功能合并在一个文件中
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import List, Optional


class SimpleEmailSender:
    """简单的邮件发送类"""
    
    def __init__(self):
        self.smtp_server = "smtp-mail.outlook.com"
        self.smtp_port = 587
        
    def send_email(self, 
                   sender_email: str,
                   sender_password: str,
                   recipient_emails: List[str],
                   subject: str,
                   body: str,
                   cc_emails: Optional[List[str]] = None):
        """
        发送文本邮件
        
        Args:
            sender_email: 发件人邮箱
            sender_password: 发件人密码
            recipient_emails: 收件人邮箱列表
            subject: 邮件主题
            body: 邮件正文
            cc_emails: 抄送邮箱列表（可选）
        """
        try:
            # 创建邮件对象
            message = MIMEMultipart()
            message["From"] = sender_email
            message["To"] = ", ".join(recipient_emails)
            message["Subject"] = subject
            
            if cc_emails:
                message["Cc"] = ", ".join(cc_emails)
            
            # 添加邮件正文（纯文本）
            message.attach(MIMEText(body, "plain", "utf-8"))
            
            # 准备收件人列表
            all_recipients = recipient_emails.copy()
            if cc_emails:
                all_recipients.extend(cc_emails)
            
            # 创建安全连接并发送邮件
            context = ssl.create_default_context()
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls(context=context)
                server.login(sender_email, sender_password)
                server.sendmail(sender_email, all_recipients, message.as_string())
            
            print(f"✅ 邮件发送成功！收件人: {', '.join(recipient_emails)}")
            return True
            
        except Exception as e:
            print(f"❌ 邮件发送失败: {str(e)}")
            return False


def send_simple_email():
    """发送简单文本邮件"""
    print("=== 发送简单文本邮件 ===")
    
    sender = SimpleEmailSender()
    
    # 邮箱配置

    sender_email = "<EMAIL>"
    sender_password = "Q_q<Lyf>[Yech]t,t"
    recipient_emails = ["<EMAIL>"]  # 发送给自己测试
    
    success = sender.send_email(
        sender_email=sender_email,
        sender_password=sender_password,
        recipient_emails=recipient_emails,
        subject="报告下载成功",
        body="这是一封通过 Python 发送的简单测试邮件！\n\n发送时间：2024年\n\n祝好！"
    )
    
    return success


# def send_custom_email(to_email, subject, message):
#     """发送自定义邮件"""
#     sender = SimpleEmailSender()
    
#     # 邮箱配置
#     sender_email = "<EMAIL>"
#     sender_password = "LyfJason!999"
    
#     success = sender.send_email(
#         sender_email=sender_email,
#         sender_password=sender_password,
#         recipient_emails=[to_email],
#         subject=subject,
#         body=message
#     )
    
#     return success


def main():
    """主函数"""
    print("🚀 简单邮件发送工具")
    print("=" * 30)
    
    # 发送测试邮件
    success = send_simple_email()
    
    if success:
        print("🎉 邮件发送功能正常工作！")
    else:
        print("⚠️ 邮件发送失败，请检查配置。")
    
    print("\n💡 使用提示：")
    print("- 修改 send_custom_email() 函数来发送自定义邮件")
    print("- 可以调用 send_custom_email('收件人@example.com', '主题', '内容')")


if __name__ == "__main__":
    main()
    
    # 示例：发送自定义邮件（取消注释下面的代码来使用）
    # send_custom_email("<EMAIL>", "测试主题", "这是邮件内容")
