#!/usr/bin/env python3
from datetime import datetime, timedelta
import pandas as pd
import os
import json
import platform
from fastapi import FastAPI, HTTPException, Query
from msal import PublicClientApplication, ConfidentialClientApplication
import uvicorn
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import hashlib
import win32com.client 
import pythoncom
import traceback
import gc
import csv
from fastapi.responses import FileResponse
from typing import Optional, Tuple, List, Dict
import time
from fastapi import Body
from pydantic import BaseModel
import uuid
import msal


# 创建FastAPI应用
app = FastAPI(
    title="PowerBI数据下载工具",
    version="1.0"
)

# PowerBI REST API配置
client_id = "ea0616ba-638b-4df5-95b9-636659ae5121"
# client_id = "7f9cf77f-ae03-4b51-9844-1df84fdcd899"
client_secret = "****************************************"
tenant_id = "3bfeb222-e42c-4535-aace-ea6f7751369b"
workspace_name = "DNA GCA_DEV:TW MAJOR REPORT"

#配置文件的地址
# base_dir = "C:/Users/<USER>/OneDrive - adidas/Desktop/code"
# base_dir = "C:/Users/<USER>/Desktop/code"
base_dir = "C:/Users/<USER>/Desktop/test"

TIME_DIMENSION_MAP = {
    "Snapshot Date": "date",
    "DATE": "date",
    "MONTH": "month",
    "QUARTER": "quarter",
    "YEAR": "year"
}

TEMP_DIR = "temp_batches_practical"
def get_default_download_dir():
    system = platform.system()
    if system == "Windows":
        return os.path.join(os.path.expanduser("~"), "Desktop", "测试", "实用优化下载")
    elif system == "Darwin":  # macOS
        return os.path.join(os.path.expanduser("~"), "Desktop", "PowerBI实用优化下载")
    else:  # Linux
        return os.path.join(os.path.expanduser("~"), "Downloads", "PowerBI实用优化下载")

custom_dir = get_default_download_dir()
temp_dir = os.path.join(custom_dir, TEMP_DIR)

# 确保目录存在
os.makedirs(custom_dir, exist_ok=True)
os.makedirs(temp_dir, exist_ok=True)

#获取access_token 客户端凭证模式
# def get_access_token():
#     try:
#         authority = f"https://login.microsoftonline.com/{tenant_id}"
#         app = ConfidentialClientApplication(client_id=client_id, client_credential=client_secret, authority=authority)
#         scope = ["https://analysis.windows.net/powerbi/api/.default"]
        
#         print("🔄 正在获取token...")
#         result = app.acquire_token_for_client(scopes=scope)
        
#         if result and "access_token" in result:
#             token = result["access_token"]
#             print(f"📅 Token过期时间: {result.get('expires_in', 'Unknown')}秒")
#             return token
#         else:
#             error_msg = result.get("error_description", "Unknown error") if result else "No response from MSAL"
#             print(f"❌ 获取token失败: {error_msg}")
#             print(f"❌ 错误详情: {result}")
#             raise Exception(f"Failed to get token: {error_msg}")
#     except Exception as e:
#         print(f"❌ 获取access_token异常: {e}")
#         raise

#获取access_token 弹框登录模式
def get_access_token():
    try:
        authority = "https://login.microsoftonline.com/common"
        app = msal.PublicClientApplication(client_id=client_id, authority=authority)
        scopes = ["https://analysis.windows.net/powerbi/api/.default"]
        
        print("🔄 正在获取token...")
        result = app.acquire_token_interactive(scopes=scopes)
        
        if "access_token" in result:
            token = result["access_token"]
            print(f"📅 Token过期时间: {result.get('expires_in', 'Unknown')}秒")
            return token
        else:
            error_msg = result.get("error_description", "Unknown error")
            print(f"❌ 获取token失败: {error_msg}")
            print(f"❌ 错误详情: {result}")
            raise Exception(f"Failed to get token: {error_msg}")
    except Exception as e:
        print(f"❌ 获取access_token异常: {e}")
        raise

# 加载映射 - 使用正确的路径
def load_json_mapping(filename):
    file_path = os.path.join(base_dir, filename)
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def get_mapping_files(name):
    if name == "TW Inventory_import":
        dim_file = 'dim_mapping_inventory.json'
        fcst_file = 'fcst_mapping_inventory.json'
        filter_file = 'filter_mapping_inventory.json'
    elif name == "TW Transaction Template":
        dim_file = 'dim_mapping_transaction.json'
        fcst_file = 'fcst_mapping_transaction.json'
        filter_file = 'filter_mapping_transaction.json'
    else:
        raise ValueError(f"未知的name参数: {name}")
    return dim_file, fcst_file, filter_file

PRACTICAL_CONFIG_LIST = load_json_mapping('practical_config.json')

def load_all_mappings(name):
    dim_file, fcst_file, filter_file = get_mapping_files(name)
    dim_mapping = load_json_mapping(dim_file)
    fcst_mapping = load_json_mapping(fcst_file)
    filter_mapping = load_json_mapping(filter_file)
    return dim_mapping, fcst_mapping, filter_mapping

def build_dim_actual_names(dim_mapping):
    dim_actual_names = {}
    for dim, expr in dim_mapping.items():
        table_part, column_part = expr.split('[', 1)
        table_name = table_part.strip("'")
        column_name = column_part.rstrip(']')
        actual_name = f"{table_name}[{column_name}]"
        dim_actual_names[dim] = actual_name
    return dim_actual_names

def build_fcst_actual_names(fcst_mapping):
    fcst_actual_names = {}
    for fcst, expr in fcst_mapping.items():
        if '[' in expr and ']' in expr:
            start = expr.find('[') + 1
            end = expr.find(']', start)
            if end != -1:
                metric_name = expr[start:end]
                actual_name = f"[{metric_name}]"
            else:
                actual_name = expr.split(',')[0].strip('"')
        else:
            actual_name = expr.split(',')[0].strip('"')
        fcst_actual_names[fcst] = actual_name
    return fcst_actual_names


#根据自然天计算季度的开始和结束日期
def get_quarter_start_end(year, quarter):
    # 计算季度开始月份
    start_month = (quarter - 1) * 3 + 1
    
    # 计算季度结束月份
    end_month = start_month + 2
    
    # 季度开始日期是该月的第一天
    start_date = datetime(year, start_month, 1)
    
    # 季度结束日期是下一季度的前一天
    if end_month == 12:
        end_date = datetime(year+1, 1, 1) - timedelta(days=1)
    else:
        end_date = datetime(year, end_month+1, 1) - timedelta(days=1)
    
    return start_date, end_date

#获取日期所在的季度
def get_quarter(dt):
    return (dt.month - 1) // 3 + 1

# 根据时间维度和时间跨度获取实用优化配置
def get_practical_config(batch_type, total_units):

    for config in PRACTICAL_CONFIG_LIST:
        if config[0] == batch_type and config[1] <= total_units <= config[2]:
            return config[3], config[4]
    
    # 如果没有匹配项，返回默认配置
    default_config = {
        'date': (5, 10),
        'month': (5, 10),
        'quarter': (5, 10),
        'year': (1, 1)
    }
    return default_config.get(batch_type, (10, 10))

# 计算时间跨度并配置优化参数
def calculate_and_configure_batch(batch_type, start_date, end_date):
  
    if batch_type == 'date':
        total_units = (end_date - start_date).days + 1
        unit_name = "天"
        
    elif batch_type == 'month':
        # 计算月份数
        total_units = (end_date.year - start_date.year) * 12 + (end_date.month - start_date.month) + 1
        unit_name = "个月"
        
    elif batch_type == 'quarter':
        # 计算季度数
        start_quarter = (start_date.month - 1) // 3 + 1
        end_quarter = (end_date.month - 1) // 3 + 1
        total_units = (end_date.year - start_date.year) * 4 + (end_quarter - start_quarter) + 1
        unit_name = "个季度"
        
    elif batch_type == 'year':
        # 计算年数
        total_units = end_date.year - start_date.year + 1
        unit_name = "年"
    else:
        # 默认值处理
        total_units = (end_date - start_date).days + 1
        unit_name = "天"
        batch_type = 'date'  # 确保有有效的batch_type
    
    # 打印时间跨度信息
    print(f"📅📅 时间跨度: {total_units} {unit_name}")
    
    # 获取优化配置
    PRACTICAL_BATCH, PRACTICAL_CONCURRENT = get_practical_config(batch_type, total_units)
    
    # 打印优化配置
    print(f"⚡⚡ 实用优化配置: {PRACTICAL_BATCH}{unit_name.replace('个', '')}批次 | {PRACTICAL_CONCURRENT}并发")
    
    return total_units, unit_name, PRACTICAL_BATCH, PRACTICAL_CONCURRENT

#生成实用优化的日期批次，支持按日/月/季度/年分批
def generate_practical_date_batches(start_date_str, end_date_str,batch_type,PRACTICAL_BATCH):
    request_uuid = uuid.uuid4().hex[:8]
    print(f"请求总批次:{request_uuid}")
    try:
        # 解析日期
        start_parts = start_date_str.split(',')
        end_parts = end_date_str.split(',')
        
        if len(start_parts) != 3 or len(end_parts) != 3:
            raise ValueError("日期格式错误，应为 YYYY,MM,DD")
        
        start_date = datetime(int(start_parts[0]), int(start_parts[1]), int(start_parts[2]))
        end_date = datetime(int(end_parts[0]), int(end_parts[1]), int(end_parts[2]))
        
        batches = []
        
        # 按天分批
        if batch_type == 'date':
            current_date = start_date
            while current_date <= end_date:
                batch_end_date = min(current_date + timedelta(days=PRACTICAL_BATCH-1), end_date)
                
                batch_start_str = f"{current_date.year},{current_date.month},{current_date.day}"
                batch_end_str = f"{batch_end_date.year},{batch_end_date.month},{batch_end_date.day}"
                
                uuidSrt = uuid.uuid4().hex[:8]
                batch_id = hashlib.md5(f"DATE_{uuidSrt}_{batch_start_str}_{batch_end_str}".encode()).hexdigest()[:16]
                batch_ids = f"{request_uuid}_{batch_id}"
                batches.append({
                    'batch_id': batch_ids,
                    'batch_num': len(batches) + 1,
                    'start_date': batch_start_str,
                    'end_date': batch_end_str,
                    'date_display': f"{current_date.strftime('%Y-%m-%d')} 到 {batch_end_date.strftime('%Y-%m-%d')}",
                    'days_count': (batch_end_date - current_date).days + 1
                })
                
                current_date = batch_end_date + timedelta(days=1)
        
        # 按月分批
        elif batch_type == 'month':
            # 从用户指定的日期开始，而不是当月的第一天
            current_date = start_date
            batch_counter = 0
            batch_start_date = None
            
            while current_date <= end_date:
                batch_counter += 1
                
                # 如果是批次开始的第一天，设置批次开始日期
                if batch_start_date is None:
                    batch_start_date = current_date
                
                # 计算下个月的第一天
                if current_date.month == 12:
                    next_month = current_date.replace(year=current_date.year+1, month=1, day=1)
                else:
                    next_month = current_date.replace(month=current_date.month+1, day=1)
                
                # 批次结束日期是下个月的第一天减一天或用户指定的结束日期
                batch_end_date = min(next_month - timedelta(days=1), end_date)
                
                # 当达到批次大小（PRACTICAL_BATCH个月）或者当前月份是最后一个月份时，添加批次
                if batch_counter % PRACTICAL_BATCH == 0 or next_month > end_date:
                    # 使用精确的批次开始日期（用户指定的日期）
                    batch_start_str = f"{batch_start_date.year},{batch_start_date.month},{batch_start_date.day}"
                    batch_end_str = f"{batch_end_date.year},{batch_end_date.month},{batch_end_date.day}"
                    
                    # 计算批次包含的月份数
                    months_in_batch = batch_counter
                    
                    # 生成月份范围显示
                    if months_in_batch > 1:
                        date_display = f"{batch_start_date.strftime('%Y-%m-%d')} 到 {batch_end_date.strftime('%Y-%m-%d')}"
                    else:
                        date_display = f"{batch_start_date.strftime('%Y-%m-%d')} 到 {batch_end_date.strftime('%Y-%m-%d')}"

                    uuidSrt = uuid.uuid4().hex[:8]
                    batch_id = hashlib.md5(f"MONTH_{uuidSrt}_{batch_start_str}_{batch_end_str}".encode()).hexdigest()[:16]
                    batch_ids = f"{request_uuid}_{batch_id}"
                    
                    batches.append({
                        'batch_id': batch_ids,
                        'batch_num': len(batches) + 1,
                        'start_date': batch_start_str,
                        'end_date': batch_end_str,
                        'date_display': date_display,
                        'days_count': (batch_end_date - batch_start_date).days + 1,
                        'months_count': months_in_batch
                    })
                    
                    # 重置批次计数和批次开始日期
                    batch_counter = 0
                    batch_start_date = None
                
                # 移动到下个月的第一天
                current_date = next_month

        # 按自然季度分批
        elif batch_type == 'quarter':
            # 确定开始季度和年份
            start_quarter = get_quarter(start_date)
            start_year = start_date.year
            
            # 确定结束季度和年份
            end_quarter = get_quarter(end_date)
            end_year = end_date.year
            
            # 当前季度和年份
            current_quarter = start_quarter
            current_year = start_year
            
            # 批次计数器
            batch_counter = 1
            
            # 当前批次的开始日期和结束日期
            batch_start_date = None
            batch_end_date = None
            
            while True:
                # 获取当前季度的自然开始和结束日期
                q_start, q_end = get_quarter_start_end(current_year, current_quarter)
                
                # 如果是批次的第一个季度，设置批次的开始日期
                if batch_start_date is None:
                    # 如果是第一个季度，使用用户指定的开始日期
                    if current_year == start_year and current_quarter == start_quarter:
                        batch_start_date = start_date
                    else:
                        batch_start_date = q_start
                
                # 如果是批次的最后一个季度，设置批次的结束日期
                # 检查是否是最后一个季度
                if current_year == end_year and current_quarter == end_quarter:
                    batch_end_date = end_date
                else:
                    batch_end_date = q_end
                
                # 检查是否达到批次大小或最后一个季度
                if batch_counter % PRACTICAL_BATCH == 0 or (current_year == end_year and current_quarter == end_quarter):
                    # 创建批次
                    batch_start_str = f"{batch_start_date.year},{batch_start_date.month},{batch_start_date.day}"
                    batch_end_str = f"{batch_end_date.year},{batch_end_date.month},{batch_end_date.day}"
                    
                    # 计算批次包含的季度数
                    quarters_in_batch = batch_counter % PRACTICAL_BATCH
                    if quarters_in_batch == 0:
                        quarters_in_batch = PRACTICAL_BATCH

                    uuidSrt = uuid.uuid4().hex[:8]
                    batch_id = hashlib.md5(f"QUARTER_{uuidSrt}_{batch_start_str}_{batch_end_str}".encode()).hexdigest()[:16]
                    batch_ids = f"{request_uuid}_{batch_id}"
                    
                    batches.append({
                        'batch_id': batch_ids,
                        'batch_num': len(batches) + 1,
                        'start_date': batch_start_str,
                        'end_date': batch_end_str,
                        'date_display': f"{batch_start_date.strftime('%Y-%m-%d')} 到 {batch_end_date.strftime('%Y-%m-%d')}",
                        'days_count': (batch_end_date - batch_start_date).days + 1,
                        'quarters_count': quarters_in_batch
                    })
                    
                    # 重置批次变量
                    batch_counter = 0
                    batch_start_date = None
                
                # 移动到下一个季度
                batch_counter += 1
                current_quarter += 1
                if current_quarter > 4:
                    current_quarter = 1
                    current_year += 1
                
                # 检查是否结束
                if current_year > end_year or (current_year == end_year and current_quarter > end_quarter):
                    break

        # 按年分批
        elif batch_type == 'year':
            # 从用户指定的日期开始，而不是当年的第一天
            current_date = start_date
            batch_counter = 0
            batch_start_date = None
            
            while current_date <= end_date:
                batch_counter += 1
                
                if batch_start_date is None:
                    batch_start_date = current_date
                
                # 计算下一年的第一天
                next_year = current_date.replace(year=current_date.year+1, month=1, day=1)
                
                # 批次结束日期是下一年的第一天减一天或用户指定的结束日期
                batch_end_date = min(next_year - timedelta(days=1), end_date)
                
                # 当达到批次大小（PRACTICAL_BATCH年）或者当前年份是最后一个年份时，添加批次
                if batch_counter % PRACTICAL_BATCH == 0 or next_year > end_date:
                    # 使用精确的批次开始日期（用户指定的日期）
                    batch_start_str = f"{batch_start_date.year},{batch_start_date.month},{batch_start_date.day}"
                    batch_end_str = f"{batch_end_date.year},{batch_end_date.month},{batch_end_date.day}"
                    
                    # 计算批次包含的年数
                    years_in_batch = batch_counter
                    
                    # 生成年份范围显示
                    if years_in_batch > 1:
                        date_display = f"{batch_start_date.strftime('%Y-%m-%d')} 到 {batch_end_date.strftime('%Y-%m-%d')}"
                    else:
                        date_display = f"{batch_start_date.strftime('%Y-%m-%d')} 到 {batch_end_date.strftime('%Y-%m-%d')}"
                    
                    uuidSrt = uuid.uuid4().hex[:8]
                    batch_id = hashlib.md5(f"YEAR_{uuidSrt}_{batch_start_str}_{batch_end_str}".encode()).hexdigest()[:16]
                    batch_ids = f"{request_uuid}_{batch_id}"
                    
                    batches.append({
                        'batch_id': batch_ids,
                        'batch_num': len(batches) + 1,
                        'start_date': batch_start_str,
                        'end_date': batch_end_str,
                        'date_display': date_display,
                        'days_count': (batch_end_date - batch_start_date).days + 1,
                        'years_count': years_in_batch
                    })
                    
                    # 重置
                    batch_counter = 0
                    batch_start_date = None
                
                # 移动到下一年的第一天
                current_date = next_year

        # 不分批        
        else:
            batch_start_str = f"{start_date.year},{start_date.month},{start_date.day}"
            batch_end_str = f"{end_date.year},{end_date.month},{end_date.day}"

            uuidSrt = uuid.uuid4().hex[:8]
            batch_id = hashlib.md5(f"{uuidSrt}_{batch_start_str}_{batch_end_str}".encode()).hexdigest()[:16]
            batch_ids = f"{request_uuid}_{batch_id}"
            
            batches.append({
                'batch_id': batch_ids,
                'batch_num': len(batches) + 1,
                'start_date': batch_start_str,
                'end_date': batch_end_str,
                'date_display': f"{start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}",
                'days_count': (end_date - start_date).days + 1
            })


        total_days = (end_date - start_date).days + 1
        print(f"📅📅 生成实用优化分批计划 (类型: {batch_type}):")
        print(f"   总时间范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
        print(f"   总天数: {total_days}天")
        print(f"   分批数量: {len(batches)}个批次")
        
        if batch_type == 'date':
            print(f"   优化批次策略: 按{PRACTICAL_BATCH}天分批")
        elif batch_type == 'month':
            print(f"   优化批次策略: 按{PRACTICAL_BATCH}个月分批")
        elif batch_type == 'quarter':
            print(f"   优化批次策略: 按{PRACTICAL_BATCH}个季度分批")    
        elif batch_type == 'year':
            print(f"   优化批次策略: 按{PRACTICAL_BATCH}年分批")
        
        return batches, request_uuid
        
    except Exception as e:
        print(f"❌❌ 生成日期批次失败: {e}")
        import traceback
        traceback.print_exc()
        return [], ""

def build_dax_filter(filter_mapping, **kwargs):

    filter_clauses = []
    for key, value in kwargs.items():
        if value and key in filter_mapping:
            dax_field = filter_mapping[key]
            if isinstance(value, list):
                value_set = "{" + ",".join([f'\"{v}\"' for v in value]) + "}"
                filter_clauses.append(f"{dax_field} in {value_set}")
            else:
                filter_clauses.append(f"{dax_field} = \"{value}\"")
    if filter_clauses:
        return ",\n                " + ",\n                ".join(filter_clauses)
    return ""

# 拼接DAX查询
def build_practical_dax(dim_list, fcst_list, start_date, end_date, dim_mapping, fcst_mapping, pbi_name, dax_filter=""):
    if not dim_list or not fcst_list:
        raise ValueError("维度和指标参数不能为空")
    
    # 构建维度字段列表
    dim_fields = []
    for dim in dim_list:
        if dim not in dim_mapping:
            raise ValueError(f"无效的维度字段: {dim}")
        dim_fields.append(dim_mapping[dim])
    
    # 构建指标字段列表
    fcst_fields = []
    for fcst in fcst_list:
        if fcst not in fcst_mapping:
            raise ValueError(f"无效的指标字段: {fcst}")
        fcst_fields.append(fcst_mapping[fcst])

    # 分割字符串并取逗号前的部分
    dim_column=[s.replace("'",'')  for s in dim_fields]
    fields_col = [s.split('",')[0][1:] for s in fcst_fields]
    dim_column.extend(fields_col)

    try:
        # 解析日期
        start_parts = start_date.split(',')
        end_parts = end_date.split(',')
        
        if len(start_parts) != 3 or len(end_parts) != 3:
            raise ValueError("日期格式错误，应为 YYYY,MM,DD")
        
        if pbi_name == "TW Inventory_import":
            date_table = "'Dim_date'[snapshot_date]"
        elif pbi_name == "TW Transaction Template":
            date_table = "'vw_dim_calendar'[calendar_day]"
        else:
            raise ValueError(f"未知的报表名称名称: {pbi_name}")

        dax_query = f"""EVALUATE
            CALCULATETABLE(
                SUMMARIZECOLUMNS(
                    {", ".join(dim_fields)},
                    {", ".join(fcst_fields)}
                ),
                DATESBETWEEN(
                    {date_table},
                    DATE({start_parts[0]}, {start_parts[1]}, {start_parts[2]}),
                    DATE({end_parts[0]}, {end_parts[1]}, {end_parts[2]})
                ){dax_filter}
            )"""

        return dax_query
    except Exception as e:
        print(f"❌ DAX查询构建失败: {e}")
        raise

# 查询
def fetch_data(dim_list, fcst_list, dax_query, batch_info, access_token, dim_actual_names, fcst_actual_names,pbi_name):
    print(f"dax_query: {dax_query}")
    conn = None
    rs = None
    try:
        # 线程中初始化COM
        pythoncom.CoInitialize()

        # 创建连接
        # start_connect = time.time()
        conn = win32com.client.Dispatch("ADODB.Connection")
        conn.ConnectionTimeout = 60
        
        # 修改连接字符串格式，使用更标准的access_token配置
        conn_str = (
            f"Provider=MSOLAP;"
            f"Data Source=powerbi://api.powerbi.com/v1.0/myorg/{workspace_name};"
            f"Initial Catalog={pbi_name};"
            f"Password={access_token};"
            f"Persist Security Info=True;"
            f"Impersonation Level=Impersonate;"
            f"Connect Timeout=180;"
            f"Timeout=300;"
            f"Transport Compression=Compressed;"
        )
        
        conn.Open(conn_str)

        # 创建命令对象
        cmd = win32com.client.Dispatch("ADODB.Command")
        cmd.ActiveConnection = conn
        cmd.CommandTimeout = 600
 
        print(f"⚡ 执行批次 {batch_info['batch_num']}: {batch_info['date_display']}")
        cmd.CommandText = dax_query

        result = cmd.Execute()
        print("dax执行完成开始转换===")
        if isinstance(result, tuple):
            rs = result[0]  # 如果是元组，取第一个元素
        else:
            rs = result  # 如果不是元组，直接使用

        # 检查记录集状态
        if rs.BOF and rs.EOF:
            print("查询结果为空记录集")
            df = pd.DataFrame()
            columns = []
            return None
        else:
            columns = [f.Name for f in rs.Fields]

            # 分块读取数据节省内存，每块处理5万行
            chunk_size = 50000
            all_data = []
            
            # 记录指针在开头
            if not rs.BOF:
                rs.MoveFirst()
            
            # 分块读取记录集
            while not rs.EOF:
                chunk = rs.GetRows(chunk_size)
                if not chunk or len(chunk) == 0 or len(chunk[0]) == 0:
                    break
                    
                chunk_df = pd.DataFrame({columns[i]: chunk[i] for i in range(len(columns))})
                all_data.append(chunk_df)
        
            df = pd.concat(all_data, ignore_index=True) if all_data else pd.DataFrame(columns=columns)
        
        row_count, col_count = df.shape
        print(f"获取数据: {row_count}行, {col_count}列")
 
        # 仅当有数据时才进行列映射
        if not df.empty:
            column_mapping = {
                **{dim_actual_names.get(dim): dim for dim in dim_list if dim in dim_actual_names},
                **{fcst_actual_names.get(fcst): fcst for fcst in fcst_list if fcst in fcst_actual_names}
            }
            df.rename(columns={k: v for k, v in column_mapping.items() if k in df.columns}, inplace=True)

        print(f"\n开始下载数据")

        if df is not None and not df.empty:
                    batch_rows = len(df)
                    print(f"✅ 批次号{batch_info['batch_id']} 批次 {batch_info['batch_num']} 完成: {batch_rows:,} 行")

                    for col in df.columns:
                        if pd.api.types.is_datetime64_any_dtype(df[col]):
                            try:
                                # 统一转换为无时区的时间格式
                                df[col] = df[col].dt.tz_convert(None)
                            except AttributeError:
                                # 如果转换失败，直接转换为字符串
                                df[col] = df[col].astype(str)

                    # 保存临时文件
                    output_file = os.path.join(temp_dir, f"batch_{batch_info['batch_id']}.csv")
                    df.to_csv(output_file, index=False, encoding='utf-8-sig')
                    del df
                    gc.collect()
                    return output_file
        else:
            print(f"⚠️  批次 {batch_info['batch_num']} 返回空数据")
            return None
 
    except Exception as e:
        # 统一错误处理
        error_trace = traceback.format_exc()
        print(f"数据处理失败:\n{error_trace}")
        return None
 
    finally:
        # 确保关闭资源
        try:
            if rs is not None and hasattr(rs, 'State') and rs.State == 1:
                rs.Close()
            if conn is not None and conn.State == 1:
                conn.Close()
        except Exception as e:
            print(f"关闭资源时出错: {str(e)}")
        
        pythoncom.CoUninitialize()

#实用优化数据获取主函数
async def fetch_practical_data(dim_params, fcst_params, date_min, date_max, batch_type, PRACTICAL_BATCH, PRACTICAL_CONCURRENT,
                               dim_mapping, fcst_mapping, dim_actual_names, fcst_actual_names, pbi_name,
                               dax_filter=""):
    try:
        dim_list = [d.strip() for d in dim_params.split(',')]
        fcst_list = [f.strip() for f in fcst_params.split(',')]
        # access_token = get_access_token()
        access_token = None

        batches, request_uuid = generate_practical_date_batches(date_min, date_max, batch_type, PRACTICAL_BATCH)
        if not batches:
            raise ValueError("generate_practical_date_batches 返回了空列表")
        print(f"\n🚀 开始执行 {len(batches)} 个批次下载")
        print(f"⚡ 实用优化配置: {PRACTICAL_CONCURRENT}并发 | {PRACTICAL_BATCH}天/批次 ")
        successful_batches = 0
        failed_batches = 0
        start_time = time.time()
        with ThreadPoolExecutor(max_workers=PRACTICAL_CONCURRENT) as executor:
            future_to_batch = {}
            for batch in batches:
                print(f"批次batch_num: {batch['batch_num']} ,批次batch_id:  {batch['batch_id']},批次start_date:  {batch['start_date']} ,批次end_date:  {batch['end_date']}")
                dax_query = build_practical_dax(dim_list, fcst_list, batch['start_date'], batch['end_date'], dim_mapping, fcst_mapping, pbi_name, dax_filter)
                dim_list = [d.strip() for d in dim_params.split(',')]
                fcst_list = [f.strip() for f in fcst_params.split(',')]
                future = executor.submit(fetch_data, dim_list, fcst_list, dax_query, batch, access_token, dim_actual_names, fcst_actual_names, pbi_name)
                future_to_batch[future] = batch
            batch_files = []
            for future in as_completed(future_to_batch):
                batch = future_to_batch[future]
                try:
                    file_path = future.result()
                    if file_path is not None:
                        batch_files.append(f"batch_{batch['batch_id']}.csv")
                        successful_batches += 1
                        progress_pct = (successful_batches / len(batches)) * 100
                        print(f"   📈 进度: {progress_pct:.1f}% | 成功: {successful_batches}/{len(batches)}")
                    else:
                        failed_batches += 1
                        print(f"   ❌ 批次失败，继续...")
                except Exception as batch_error:
                    failed_batches += 1
                    print(f"❌ 批次 {batch['batch_num']} 处理异常: {batch_error}")
                    continue
        if successful_batches == 0:
            raise Exception("所有批次都失败了，无数据可合并")
        start_connect = time.time()
        print(f"\n🔄 合并 {successful_batches} 个批次的数据...")
        output_file = merge_batch_files(batch_files, pbi_name)
        time_connect = time.time() - start_connect
        print(f"\n🔄 合并 完成耗时: {time_connect:.2f}秒")
        file_size = os.path.getsize(output_file)
        file_size_mb = round(file_size / (1024 * 1024), 2)
        rows, cols = get_file_stats(output_file)
        cleanup_temp_files(request_uuid)
        end_time = time.time()
        total_time = end_time - start_time
        hours = int(total_time // 3600)
        minutes = int((total_time % 3600) // 60)
        seconds = int(total_time % 60)
        time_str = f"{hours}小时{minutes}分{seconds}秒" if hours > 0 else f"{minutes}分{seconds}秒"
        print(f"\n✅ 实用优化下载完成!")
        print(f"📁 文件路径: {output_file}")
        print(f"📊 最终数据: {rows:,} 行, {cols} 列")
        print(f"📈 文件大小: {file_size_mb} MB")
        print(f"📦 成功批次: {successful_batches}/{len(batches)}")
        print(f"❌ 失败批次: {failed_batches}")
        print(f"⚡ 用时: {time_str}")
        return output_file, rows, cols, file_size_mb, time_str
    except Exception as e:
        print(f"❌ 实用优化下载失败: {e}")
        raise

#同时获取行数和列数
def get_file_stats(file_path):
    row_count, col_count = 0, 0
    with open(file_path, 'r', encoding='utf-8-sig') as f:
        reader = csv.reader(f)
        for i, row in enumerate(reader):
            if i == 0:  # 首行获取列数
                col_count = len(row)
            row_count = i  # 末行索引=实际行数
    return row_count, col_count

#流式合并批次文件
def merge_batch_files(batch_files,pbi_name):
    try:
        if not batch_files:
            return None
        
        # 创建最终合并文件
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        merged_file = os.path.join(custom_dir, f"{pbi_name}_{timestamp}.csv")

        with open(merged_file, 'w', encoding='utf-8-sig') as outfile:
            for i, batch_file in enumerate(batch_files):
                file_path = os.path.join(temp_dir, batch_file)
                if not os.path.exists(file_path):
                    print(f"⚠️  文件不存在: {batch_file}")
                    continue
                
                with open(file_path, 'r', encoding='utf-8-sig') as infile:
                    # 第一个文件保留列头
                    if i == 0:
                        outfile.write(infile.read())
                    # 后续文件跳过列头
                    else:
                        next(infile)  # 跳过第一行(列头)
                        for line in infile:
                            outfile.write(line)
                
                print(f"✅ 已合并批次: {batch_file}")
        
        # 获取合并后的行数
        row_count = sum(1 for _ in open(merged_file, encoding='utf-8-sig')) - 1
        print(f"📊 合并完成: {len(batch_files)}个文件 → {row_count}行数据")
        
        return merged_file
        
    except Exception as e:
        print(f"❌❌ 流式合并失败: {e}")
        return None

#清理本次请求的临时文件
def cleanup_temp_files(request_uuid):
    try:
        import glob
        pattern = os.path.join(temp_dir, f"batch_{request_uuid}_*.csv")
        temp_files = glob.glob(pattern)
        removed_count = 0
        
        for temp_file in temp_files:
            try:
                os.remove(temp_file)
                removed_count += 1
            except:
                pass
        
        print(f"🧹 清理临时文件: {removed_count} 个文件已删除")
        
    except Exception as e:
        print(f"⚠️  清理临时文件时出现异常: {e}")

# 将字符串转为列表
def parse_str_to_list(val):
    if val is None:
        return None
    if isinstance(val, list):
        # 如果是 ["All"]，直接返回 None
        if len(val) == 1 and val[0] == "All":
            return None
        return val
    if isinstance(val, str):
        try:
            result = json.loads(val)
            if isinstance(result, list):
                if len(result) == 1 and result[0] == "All":
                    return None
                return result
            else:
                if result == "All":
                    return None
                return [result]
        except Exception:
            if val == "All":
                return None
            return [val]
    return [val]

@app.get("/get_practical_data")
async def get_practical_data(
    name: str = Query(..., description="报表名称"),
    dim: str = Query(..., description="维度参数，用逗号分隔"),
    fcst: str = Query(..., description="指标参数，用逗号分隔"),
    damin: str = Query(..., description="开始日期，格式:YYYY/MM/DD"),
    damax: str = Query(..., description="结束日期，格式:YYYY/MM/DD"),
    launch: Optional[str] = Query(None, description="launch参数"),
    pro_type: Optional[str] = Query(None, description="pro_type参数"),
    pro_div: Optional[str] = Query(None, description="pro_div参数"),
    pro_article: Optional[str] = Query(None, description="pro_article参数"),
    pro_category: Optional[str] = Query(None, description="pro_category参数"),
    pro_yeezy: Optional[str] = Query(None, description="pro_yeezy参数"),
    sto_code: Optional[str] = Query(None, description="sto_code参数"),
    sto_status: Optional[str] = Query(None, description="sto_status参数"),
    sto_main_channel: Optional[str] = Query(None, description="sto_main_channel参数"),
    sto_format: Optional[str] = Query(None, description="sto_format参数"),
    sto_type: Optional[str] = Query(None, description="sto_type参数")
):
    try:
        timestamp1 = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        print(f"\n{'='*20} PowerBI 实用优化模式 {'='*20}")
        print(f"开始时间: {timestamp1}")
        
        if not dim or not fcst:
            raise HTTPException(
                status_code=400,
                detail="缺少必要参数: dim, fcst"
            )
        
        # 转换日期格式
        date_min = damin.replace("/", ",")
        date_max = damax.replace("/", ",")
        
        print("开始日期：", date_min)
        print("结束日期：", date_max)
        
        # 动态加载映射和实际名称
        dim_mapping, fcst_mapping, filter_mapping = load_all_mappings(name)
        dim_actual_names = build_dim_actual_names(dim_mapping)
        fcst_actual_names = build_fcst_actual_names(fcst_mapping)

        # 解析所有筛选参数（自动化）
        filter_param_names = [
            'launch', 'pro_type', 'pro_div', 'pro_article', 'pro_category', 'pro_yeezy',
            'sto_code', 'sto_status', 'sto_main_channel', 'sto_format', 'sto_type'
        ]
        filter_param_values = {}
        for param in filter_param_names:
            filter_param_values[param] = parse_str_to_list(locals()[param])
        # 生成 DAX filter 字符串
        dax_filter = build_dax_filter(
            filter_mapping,
            **filter_param_values
        )

        # 计算预期数据量
        start_parts = date_min.split(',')
        end_parts = date_max.split(',')
        start_date = datetime(int(start_parts[0]), int(start_parts[1]), int(start_parts[2]))
        end_date = datetime(int(end_parts[0]), int(end_parts[1]), int(end_parts[2]))

        # 按优先级检测时间维度字段
        batch_type = next(
            (TIME_DIMENSION_MAP[field] for field in ["Snapshot Date","DATE", "MONTH", "QUARTER", "YEAR"] 
             if field in dim),
            None  # 默认值
        )

        print(f"🔍 分批策略: {batch_type}")

        # 在需要计算时间跨度和配置的地方调用这个方法
        total_units, unit_name, PRACTICAL_BATCH, PRACTICAL_CONCURRENT = calculate_and_configure_batch(
            batch_type, start_date, end_date
        )
        
        # 获取数据并生成CSV文件
        file_path, row_count, col_count, file_size_mb, time_str = await fetch_practical_data(
            dim, fcst, date_min, date_max, batch_type, PRACTICAL_BATCH, PRACTICAL_CONCURRENT,
            dim_mapping, fcst_mapping, dim_actual_names, fcst_actual_names, name,
            dax_filter=dax_filter
        )
        # 计算运行时间
        timestamp2 = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        print(f"\n{'='*50}")
        print(f"🎉 下载完成: {timestamp2}")
        print(f"⏱️ 总运行时长: {time_str}")
        print(f"📊 最终数据: {row_count:,}行, {col_count}列")
        print(f"💾 CSV文件大小: {file_size_mb} MB")
        print(f"📁 文件路径: {file_path}")
        print(f"💻 操作系统: {platform.system()}")
        print(f"{'='*50}")

        # 获取文件名用于下载
        filename = os.path.basename(file_path)
        #  检查文件是否存在
        if not file_path or not os.path.exists(file_path):
            raise HTTPException(status_code=500, detail="生成的CSV文件不存在")
        
        # 返回文件响应
        return FileResponse(
            path=file_path,
            filename=filename,
            media_type="text/csv",
            headers={
                "Content-Disposition": f"attachment; filename={filename}",
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        error_msg = f"实用优化下载失败: {str(e)}"
        print(f"❌❌❌❌ {error_msg}")
        raise HTTPException(status_code=500, detail=error_msg)


if __name__ == "__main__":
    print("🚀 PowerBI数据下载工具 - 启动中...")
    print(f"💾 下载目录: {custom_dir}")

    # 多进程并发，启动命令行：
    # uvicorn pbi_download:app --host ************* --port 5000 --workers 2
    # 单进程启动命令行：
    # uvicorn.run(app, host="*************", port=5000)
    uvicorn.run(app, host="localhost", port=5000)