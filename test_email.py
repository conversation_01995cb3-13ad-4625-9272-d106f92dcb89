from send_email import send_custom_email

def test_send_email(url):
    """发送包含URL的邮件"""

    # 邮件参数
    to_email = "<EMAIL>"  # 收件人
    subject = "报告下载成功"  # 邮件主题

    # 使用 format() 方法替换 {url} 变量
    message = """
                您好，

                您的报告已经成功下载并处理完成。

                详细信息：
                - 状态：成功
                - 报告地址：{url}

                如有任何问题，请及时联系。

                祝好！
                    """.strip().format(url=url)

    print(f"📧 准备发送邮件到: {to_email}")
    print(f"📝 邮件主题: {subject}")
    print(f"🔗 报告地址: {url}")
    print("📄 邮件内容:")
    print(message)
    print()

    success = send_custom_email(to_email, subject, message)

    if success:
        print("✅ 邮件发送成功！")
    else:
        print("❌ 邮件发送失败！")

    return success

def test_send_email_f_string(url):
    """使用 f-string 的替代方案"""

    # 邮件参数
    to_email = "<EMAIL>"
    subject = "报告下载成功 (f-string版本)"

    # 使用 f-string 直接在字符串中插入变量
    message = f"""
                您好，

                您的报告已经成功下载并处理完成。

                详细信息：
                - 状态：成功
                - 报告地址：{url}
                - 生成时间：2024年7月31日

                如有任何问题，请及时联系。

                祝好！
                系统自动发送
                    """.strip()

    print(f"📧 使用 f-string 发送邮件到: {to_email}")
    print(f"🔗 报告地址: {url}")

    success = send_custom_email(to_email, subject, message)
    return success

def main():
    """主函数 - 测试两种字符串格式化方法"""
    url = "https://www.baidu.com/"

    print("🚀 测试邮件发送 - URL变量替换")
    print("=" * 40)

    # 方法1: 使用 .format()
    print("📧 方法1: 使用 .format() 方法")
    success1 = test_send_email(url)

    print("\n" + "-" * 40)

    # 方法2: 使用 f-string
    print("📧 方法2: 使用 f-string")
    success2 = test_send_email_f_string(url)

    print("\n" + "=" * 40)
    print("📊 发送结果:")
    print(f"format() 方法: {'✅ 成功' if success1 else '❌ 失败'}")
    print(f"f-string 方法: {'✅ 成功' if success2 else '❌ 失败'}")

if __name__ == "__main__":
    main()