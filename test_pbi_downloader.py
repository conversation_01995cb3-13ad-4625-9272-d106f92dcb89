#!/usr/bin/env python3
"""
PowerBI 下载器类的测试脚本
测试 pbi_downloader_class.py 中的 PowerBIDownloader 类
"""

from pbi_downloader_class import PowerBIDownloader
import json
from datetime import datetime


def test_inventory_download():
    """测试库存报表下载"""
    print("🧪 测试库存报表下载")
    print("=" * 50)
    
    try:
        # 创建下载器实例
        downloader = PowerBIDownloader()
        
        # 库存报表的JSON参数
        json_data = {
            "report_name": "TW Inventory",
            "dim": "Snapshot Date,Article No",
            "fcst": "Total Stock Qty",
            "Snapshot_date": ["2024/01/01", "2024/01/02", "2024/01/03"],
            "article_no": ["All"],
            "stock_category": ["All"]
        }
        
        print("📋 测试参数:")
        print(json.dumps(json_data, indent=2, ensure_ascii=False))
        print()
        
        # 执行下载
        print("🚀 开始下载...")
        result = downloader.download_data(json_data)
        
        print("✅ 库存报表下载测试成功！")
        print(f"📊 结果: {result}")
        return True
        
    except Exception as e:
        print(f"❌ 库存报表下载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


# def test_transaction_download():
#     """测试交易报表下载"""
#     print("\n🧪 测试交易报表下载")
#     print("=" * 50)
    
#     try:
#         # 创建下载器实例
#         downloader = PowerBIDownloader()
        
#         # 交易报表的JSON参数
#         json_data = {
#             "report_name": "TW Transaction Template",
#             "dim": "DATE,Article No",
#             "fcst": "Sales Qty",
#             "damin": "2024/01/01",
#             "damax": "2024/01/07",
#             "pro_type": ["All"],
#             "sto_main_channel": ["All"]
#         }
        
#         print("📋 测试参数:")
#         print(json.dumps(json_data, indent=2, ensure_ascii=False))
#         print()
        
#         # 执行下载
#         print("🚀 开始下载...")
#         result = downloader.download_data(json_data)
        
#         print("✅ 交易报表下载测试成功！")
#         print(f"📊 结果: {result}")
#         return True
        
#     except Exception as e:
#         print(f"❌ 交易报表下载测试失败: {e}")
#         import traceback
#         traceback.print_exc()
#         return False


# def test_class_methods():
#     """测试类的各个方法"""
#     print("\n🧪 测试类方法")
#     print("=" * 50)
    
#     try:
#         # 创建下载器实例
#         downloader = PowerBIDownloader()
        
#         # 测试映射加载
#         print("📋 测试映射加载...")
#         dim_mapping, fcst_mapping, filter_mapping = downloader.load_all_mappings("TW Inventory")
#         print(f"✅ 维度映射: {len(dim_mapping)} 个字段")
#         print(f"✅ 指标映射: {len(fcst_mapping)} 个字段")
#         print(f"✅ 过滤映射: {len(filter_mapping)} 个字段")
        
#         # 测试实际名称构建
#         print("\n📋 测试实际名称构建...")
#         dim_actual_names = downloader.build_dim_actual_names(dim_mapping)
#         fcst_actual_names = downloader.build_fcst_actual_names(fcst_mapping)
#         print(f"✅ 维度实际名称: {len(dim_actual_names)} 个")
#         print(f"✅ 指标实际名称: {len(fcst_actual_names)} 个")
        
#         # 测试字符串解析
#         print("\n📋 测试字符串解析...")
#         test_cases = [
#             "All",
#             '["A", "B", "C"]',
#             ["X", "Y"],
#             None
#         ]
        
#         for test_case in test_cases:
#             result = downloader.parse_str_to_list(test_case)
#             print(f"   {test_case} → {result}")
        
#         # 测试日期计算
#         print("\n📋 测试日期计算...")
#         test_date = datetime(2024, 7, 15)
#         quarter = downloader.get_quarter(test_date)
#         print(f"✅ 日期 {test_date.strftime('%Y-%m-%d')} 属于第 {quarter} 季度")
        
#         start_date, end_date = downloader.get_quarter_start_end(2024, 3)
#         print(f"✅ 2024年第3季度: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
        
#         print("✅ 类方法测试完成！")
#         return True
        
#     except Exception as e:
#         print(f"❌ 类方法测试失败: {e}")
#         import traceback
#         traceback.print_exc()
#         return False


# def test_json_string_input():
#     """测试JSON字符串输入"""
#     print("\n🧪 测试JSON字符串输入")
#     print("=" * 50)
    
#     try:
#         # 创建下载器实例
#         downloader = PowerBIDownloader()
        
#         # JSON字符串格式
#         json_string = '''
#         {
#             "report_name": "TW Inventory",
#             "dim": "Snapshot Date,Article No",
#             "fcst": "Total Stock Qty",
#             "Snapshot_date": ["2024/01/01"],
#             "article_no": ["All"],
#             "stock_category": ["All"]
#         }
#         '''
        
#         print("📋 JSON字符串:")
#         print(json_string)
        
#         # 执行下载
#         print("🚀 开始下载...")
#         result = downloader.download_data(json_string)
        
#         print("✅ JSON字符串输入测试成功！")
#         print(f"📊 结果: {result}")
#         return True
        
#     except Exception as e:
#         print(f"❌ JSON字符串输入测试失败: {e}")
#         import traceback
#         traceback.print_exc()
#         return False


# def test_filter_conditions():
#     """测试过滤条件"""
#     print("\n🧪 测试过滤条件")
#     print("=" * 50)
    
#     try:
#         # 创建下载器实例
#         downloader = PowerBIDownloader()
        
#         # 带过滤条件的JSON参数
#         json_data = {
#             "report_name": "TW Inventory",
#             "dim": "Snapshot Date,Article No,Global Store Code",
#             "fcst": "Total Stock Qty",
#             "Snapshot_date": ["2024/01/01"],
#             "article_no": ["FX3624", "GZ2864"],  # 具体货号
#             "stock_category": ["FOOTWEAR"],       # 具体品类
#             "global_store_code": ["TW001"],       # 具体店铺
#             "store_type": ["RETAIL"]              # 店铺类型
#         }
        
#         print("📋 带过滤条件的参数:")
#         print(json.dumps(json_data, indent=2, ensure_ascii=False))
#         print()
        
#         # 执行下载
#         print("🚀 开始下载...")
#         result = downloader.download_data(json_data)
        
#         print("✅ 过滤条件测试成功！")
#         print(f"📊 结果: {result}")
#         return True
        
#     except Exception as e:
#         print(f"❌ 过滤条件测试失败: {e}")
#         import traceback
#         traceback.print_exc()
#         return False


def main():
    """主测试函数"""
    print("🚀 PowerBI 下载器类测试开始")
    print("=" * 60)
    
    # 测试列表
    tests = [
        ("类方法测试", test_class_methods),
        ("JSON字符串输入测试", test_json_string_input),
        ("过滤条件测试", test_filter_conditions),
        # 注意：以下测试需要实际的PowerBI连接，可能会失败
        ("库存报表下载测试", test_inventory_download),
        ("交易报表下载测试", test_transaction_download),
    ]
    
    # 执行测试
    total_tests = len(tests)
    passed_tests = 0
    
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 开始测试: {test_name}")
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} 通过")
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print(f"总测试数: {total_tests}")
    print(f"通过数: {passed_tests}")
    print(f"失败数: {total_tests - passed_tests}")
    
    if passed_tests == total_tests:
        print("🎉 所有测试都通过了！")
    else:
        print("⚠️ 部分测试失败")
    
    print("\n💡 注意:")
    print("- 实际下载测试需要有效的PowerBI连接和配置文件")
    print("- 确保配置文件路径正确")
    print("- 检查Azure AD应用权限设置")
    print("- 类已成功封装原有逻辑，保持功能不变")


if __name__ == "__main__":
    main()
