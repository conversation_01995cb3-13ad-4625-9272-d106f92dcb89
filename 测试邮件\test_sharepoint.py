from office365.sharepoint.client_context import ClientContext
from office365.runtime.auth.client_credential import ClientCredential

def test_sharepoint_connection(site_url, client_id, client_secret):
    """
    测试SharePoint站点连通性，只验证认证和基本权限
    """
    try:
        credentials = ClientCredential(client_id, client_secret)
        ctx = ClientContext(site_url).with_credentials(credentials)
        web = ctx.web
        ctx.load(web)
        ctx.execute_query()
        print("连接成功，站点标题：", web.properties['Title'])
    except Exception as e:
        print("连接失败，错误信息：", e)

# 示例调用
if __name__ == "__main__":
    site_url = "https://adidasgroup.sharepoint.com/sites/APACIT-BI_Tech_Workspce"
    client_id = "你的client_id"
    client_secret = "你的client_secret"
    test_sharepoint_connection(site_url, client_id, client_secret)