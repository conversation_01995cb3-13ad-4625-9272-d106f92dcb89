#!/usr/bin/env python3
"""
PowerBI 下载器类的测试脚本
测试 pbi_downloader_class.py 中的 PowerBIDownloader 类
"""

from pbi_downloader_class import PowerBIDownloader
import json
from datetime import datetime


def test_inventory_download():
    """测试库存报表下载"""
    print("🧪 测试库存报表下载")
    print("=" * 50)
    
    try:
        # 创建下载器实例
        downloader = PowerBIDownloader()
        
        # 库存报表的JSON参数
        json_data = {
            "report_name": "TW Inventory",
            "dim": "Snapshot Date,Article No",
            "fcst": "Total Stock Qty",
            "Snapshot_date": ["2024/01/01", "2024/01/02", "2024/01/03"],
            "article_no": ["All"],
            "stock_category": ["All"]
        }
        
        print("📋 测试参数:")
        print(json.dumps(json_data, indent=2, ensure_ascii=False))
        print()
        
        # 执行下载
        print("🚀 开始下载...")
        result = downloader.download_data(json_data)
        
        print("✅ 库存报表下载测试成功！")
        print(f"📊 结果: {result}")
        return True
        
    except Exception as e:
        print(f"❌ 库存报表下载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    success = test_inventory_download()

    print("\n" + "=" * 60)
    if success:
        print("🎉 库存报表下载测试完成！")
    else:
        print("❌ 库存报表下载测试失败！")


if __name__ == "__main__":
    main()
