#!/usr/bin/env python3
from datetime import datetime, timedelta
import pandas as pd
import os
import json
import platform
from fastapi import FastAPI, HTTPException, Query
from msal import PublicClientApplication, ConfidentialClientApplication
import uvicorn
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
import hashlib
import win32com.client 
import pythoncom
import traceback
import gc
import csv
from fastapi.responses import FileResponse
from typing import Optional, Tuple, List, Dict
import time
from fastapi import Body
from pydantic import BaseModel
import uuid
import msal


# 创建FastAPI应用
app = FastAPI(
    title="PowerBI数据下载工具",
    version="1.0"
)

# PowerBI REST API配置
client_id = "ea0616ba-638b-4df5-95b9-636659ae5121"
# client_id = "7f9cf77f-ae03-4b51-9844-1df84fdcd899"
client_secret = "****************************************"
tenant_id = "3bfeb222-e42c-4535-aace-ea6f7751369b"
workspace_name = "DNA GCA_DEV:TW MAJOR REPORT"

#配置文件的地址
base_dir = "C:/Users/<USER>/OneDrive - adidas/Desktop/code"
# base_dir = "C:/Users/<USER>/Desktop/code"
# base_dir = "C:/Users/<USER>/Desktop/test"


TEMP_DIR = "temp_batches_practical"
def get_default_download_dir():
    system = platform.system()
    if system == "Windows":
        return os.path.join(os.path.expanduser("~"), "Desktop", "测试", "实用优化下载")
    elif system == "Darwin":  # macOS
        return os.path.join(os.path.expanduser("~"), "Desktop", "PowerBI实用优化下载")
    else:  # Linux
        return os.path.join(os.path.expanduser("~"), "Downloads", "PowerBI实用优化下载")

custom_dir = get_default_download_dir()
temp_dir = os.path.join(custom_dir, TEMP_DIR)

# 确保目录存在
os.makedirs(custom_dir, exist_ok=True)
os.makedirs(temp_dir, exist_ok=True)

#获取access_token 弹框登录模式
def get_access_token():
    try:
        authority = "https://login.microsoftonline.com/common"
        app = msal.PublicClientApplication(client_id=client_id, authority=authority)
        scopes = ["https://analysis.windows.net/powerbi/api/.default"]
        
        print("🔄 正在获取token...")
        result = app.acquire_token_interactive(scopes=scopes)
        
        if "access_token" in result:
            token = result["access_token"]
            print(f"📅 Token过期时间: {result.get('expires_in', 'Unknown')}秒")
            return token
        else:
            error_msg = result.get("error_description", "Unknown error")
            print(f"❌ 获取token失败: {error_msg}")
            print(f"❌ 错误详情: {result}")
            raise Exception(f"Failed to get token: {error_msg}")
    except Exception as e:
        print(f"❌ 获取access_token异常: {e}")
        raise

def fetch_data( dax_query,pbi_name,access_token):
    conn = None
    rs = None
    try:
        # 线程中初始化COM
        pythoncom.CoInitialize()

        # 创建连接
        # start_connect = time.time()
        conn = win32com.client.Dispatch("ADODB.Connection")
        conn.ConnectionTimeout = 60
        
        # 修改连接字符串格式，使用更标准的access_token配置
        conn_str = (
            f"Provider=MSOLAP;"
            f"Data Source=powerbi://api.powerbi.com/v1.0/myorg/{workspace_name};"
            f"Initial Catalog={pbi_name};"
            f"Password={access_token};"
            f"Persist Security Info=True;"
            f"Impersonation Level=Impersonate;"
            f"Connect Timeout=180;"
            f"Timeout=300;"
            f"Transport Compression=Compressed;"
        )

        conn.Open(conn_str)

        # 创建命令对象
        cmd = win32com.client.Dispatch("ADODB.Command")
        cmd.ActiveConnection = conn
        cmd.CommandTimeout = 600
 
        cmd.CommandText = dax_query

        result = cmd.Execute()
        print("dax执行完成开始转换===")
        if isinstance(result, tuple):
            rs = result[0]  # 如果是元组，取第一个元素
        else:
            rs = result  # 如果不是元组，直接使用

        # 检查记录集状态
        if rs.BOF and rs.EOF:
            print("查询结果为空记录集")
            df = pd.DataFrame()
            columns = []
            return None
        else:
            columns = [f.Name for f in rs.Fields]

            # 分块读取数据节省内存，每块处理5万行
            chunk_size = 50000
            all_data = []
            
            # 记录指针在开头
            if not rs.BOF:
                rs.MoveFirst()
            
            # 分块读取记录集
            while not rs.EOF:
                chunk = rs.GetRows(chunk_size)
                if not chunk or len(chunk) == 0 or len(chunk[0]) == 0:
                    break
                    
                chunk_df = pd.DataFrame({columns[i]: chunk[i] for i in range(len(columns))})
                all_data.append(chunk_df)
        
            df = pd.concat(all_data, ignore_index=True) if all_data else pd.DataFrame(columns=columns)
        
        row_count, col_count = df.shape
        print(f"获取数据: {row_count}行, {col_count}列")
 
    except Exception as e:
        # 统一错误处理
        error_trace = traceback.format_exc()
        print(f"数据处理失败:\n{error_trace}")
        return None
 
    finally:
        # 确保关闭资源
        try:
            if rs is not None and hasattr(rs, 'State') and rs.State == 1:
                rs.Close()
            if conn is not None and conn.State == 1:
                conn.Close()
        except Exception as e:
            print(f"关闭资源时出错: {str(e)}")
        
        pythoncom.CoUninitialize()


@app.get("/get_practical_data")
async def get_practical_data(
):
    try:
        dax_query = """EVALUATE
                        CALCULATETABLE(
                            SUMMARIZECOLUMNS(
                                'dim_channel_group'[Channel_Group],
                                "Total Stock Qty", [Total Stock Qty]
                            ),
                            DATESBETWEEN(
                                'Dim_date'[snapshot_date],
                                DATE(2025, 7, 8),
                                DATE(2025, 7, 8)
                            )
                        )"""
        access_token = get_access_token()
        fetch_data(dax_query,"TW Inventory_import",access_token)

        # 返回文件响应
        return "下载完成"
        
    except HTTPException:
        raise
    except Exception as e:
        error_msg = f"实用优化下载失败: {str(e)}"
        print(f"❌❌❌❌ {error_msg}")
        raise HTTPException(status_code=500, detail=error_msg)


if __name__ == "__main__":
    print("🚀 PowerBI数据下载工具 - 启动中...")
    print(f"💾 下载目录: {custom_dir}")

    # 多进程并发，启动命令行：
    # uvicorn pbi_download:app --host ************* --port 5000 --workers 2
    # 单进程启动命令行：
    # uvicorn.run(app, host="*************", port=5000)
    uvicorn.run(app, host="localhost", port=5000)