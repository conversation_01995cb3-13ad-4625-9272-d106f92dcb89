#!/usr/bin/env python3
"""
PowerBI 下载器类的测试脚本
"""

from pbi_downloader_class import PowerBIDownloader
from datetime import datetime
import json


def test_inventory_download():
    """测试库存报表下载"""
    print("🧪 测试库存报表下载")
    print("=" * 40)
    
    # 创建下载器实例
    downloader = PowerBIDownloader()
    
    # 测试参数
    report_name = "TW Inventory"
    dim_params = "Snapshot Date,Article No,Global Store Code"
    fcst_params = "Stock Qty,Stock Value"
    snapshot_dates = ["2024/01/01", "2024/01/02", "2024/01/03"]
    
    # 过滤参数
    filter_params = {
        "article_no": ["A12345", "B67890"],
        "stock_category": "FOOTWEAR",
        "global_store_code": "All"
    }
    
    try:
        print(f"📊 报表名称: {report_name}")
        print(f"📏 维度参数: {dim_params}")
        print(f"📈 指标参数: {fcst_params}")
        print(f"📅 快照日期: {snapshot_dates}")
        print(f"🔍 过滤条件: {filter_params}")
        print()
        
        # 执行下载
        file_path, row_count, col_count, file_size_mb, time_str = downloader.download_data(
            report_name=report_name,
            dim_params=dim_params,
            fcst_params=fcst_params,
            snapshot_dates=snapshot_dates,
            **filter_params
        )
        
        print("✅ 库存报表下载测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 库存报表下载测试失败: {e}")
        return False


def test_transaction_download():
    """测试交易报表下载"""
    print("\n🧪 测试交易报表下载")
    print("=" * 40)
    
    # 创建下载器实例
    downloader = PowerBIDownloader()
    
    # 测试参数
    report_name = "TW Transaction Template"
    dim_params = "DATE,Article No,Store Code"
    fcst_params = "Sales Qty,Sales Value"
    date_min = "2024/01/01"
    date_max = "2024/01/07"
    
    # 过滤参数
    filter_params = {
        "pro_type": "FOOTWEAR",
        "sto_main_channel": "RETAIL",
        "launch": "All"
    }
    
    try:
        print(f"📊 报表名称: {report_name}")
        print(f"📏 维度参数: {dim_params}")
        print(f"📈 指标参数: {fcst_params}")
        print(f"📅 日期范围: {date_min} 到 {date_max}")
        print(f"🔍 过滤条件: {filter_params}")
        print()
        
        # 执行下载
        file_path, row_count, col_count, file_size_mb, time_str = downloader.download_data(
            report_name=report_name,
            dim_params=dim_params,
            fcst_params=fcst_params,
            date_min=date_min,
            date_max=date_max,
            **filter_params
        )
        
        print("✅ 交易报表下载测试成功！")
        return True
        
    except Exception as e:
        print(f"❌ 交易报表下载测试失败: {e}")
        return False


def test_mapping_functions():
    """测试映射相关功能"""
    print("\n🧪 测试映射功能")
    print("=" * 40)
    
    try:
        downloader = PowerBIDownloader()
        
        # 测试加载映射
        print("📋 测试加载映射文件...")
        dim_mapping, fcst_mapping, filter_mapping = downloader.load_all_mappings("TW Inventory")
        print(f"✅ 维度映射加载成功: {len(dim_mapping)} 个字段")
        print(f"✅ 指标映射加载成功: {len(fcst_mapping)} 个字段")
        print(f"✅ 过滤映射加载成功: {len(filter_mapping)} 个字段")
        
        # 测试构建实际名称
        print("\n📋 测试构建实际名称...")
        dim_actual_names = downloader.build_dim_actual_names(dim_mapping)
        fcst_actual_names = downloader.build_fcst_actual_names(fcst_mapping)
        print(f"✅ 维度实际名称构建成功: {len(dim_actual_names)} 个")
        print(f"✅ 指标实际名称构建成功: {len(fcst_actual_names)} 个")
        
        # 测试DAX过滤器构建
        print("\n📋 测试DAX过滤器构建...")
        filter_params = {
            "article_no": ["A12345", "B67890"],
            "stock_category": "FOOTWEAR"
        }
        dax_filter = downloader.build_dax_filter(filter_mapping, **filter_params)
        print(f"✅ DAX过滤器构建成功")
        print(f"🔍 过滤器内容: {dax_filter}")
        
        return True
        
    except Exception as e:
        print(f"❌ 映射功能测试失败: {e}")
        return False


def test_date_calculations():
    """测试日期计算功能"""
    print("\n🧪 测试日期计算功能")
    print("=" * 40)
    
    try:
        downloader = PowerBIDownloader()
        
        # 测试季度计算
        print("📅 测试季度计算...")
        test_date = datetime(2024, 7, 15)
        quarter = downloader.get_quarter(test_date)
        print(f"✅ 日期 {test_date.strftime('%Y-%m-%d')} 属于第 {quarter} 季度")
        
        # 测试季度开始结束日期
        start_date, end_date = downloader.get_quarter_start_end(2024, 3)
        print(f"✅ 2024年第3季度: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
        
        # 测试批次配置计算
        print("\n📅 测试批次配置计算...")
        start_date = datetime(2024, 1, 1)
        end_date = datetime(2024, 1, 31)
        total_units, unit_name, batch_size, concurrent = downloader.calculate_and_configure_batch(
            'date', start_date, end_date
        )
        print(f"✅ 批次配置计算成功: {total_units} {unit_name}, 批次大小: {batch_size}, 并发数: {concurrent}")
        
        return True
        
    except Exception as e:
        print(f"❌ 日期计算功能测试失败: {e}")
        return False


def test_utility_functions():
    """测试工具函数"""
    print("\n🧪 测试工具函数")
    print("=" * 40)
    
    try:
        downloader = PowerBIDownloader()
        
        # 测试字符串转列表
        print("🔧 测试字符串转列表...")
        test_cases = [
            "All",
            '["A", "B", "C"]',
            ["X", "Y"],
            None,
            "single_value"
        ]
        
        for test_case in test_cases:
            result = downloader.parse_str_to_list(test_case)
            print(f"   输入: {test_case} → 输出: {result}")
        
        print("✅ 字符串转列表测试完成")
        
        # 测试获取映射文件名
        print("\n🔧 测试获取映射文件名...")
        for report_name in ["TW Inventory", "TW Transaction Template"]:
            files = downloader.get_mapping_files(report_name)
            print(f"   {report_name}: {files}")
        
        print("✅ 获取映射文件名测试完成")
        
        return True
        
    except Exception as e:
        print(f"❌ 工具函数测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 PowerBI 下载器类测试开始")
    print("=" * 60)
    
    # 测试计数
    total_tests = 0
    passed_tests = 0
    
    # 执行各项测试
    tests = [
        ("映射功能", test_mapping_functions),
        ("日期计算功能", test_date_calculations),
        ("工具函数", test_utility_functions),
        # ("库存报表下载", test_inventory_download),  # 需要实际连接，暂时注释
        # ("交易报表下载", test_transaction_download),  # 需要实际连接，暂时注释
    ]
    
    for test_name, test_func in tests:
        total_tests += 1
        print(f"\n🧪 开始测试: {test_name}")
        try:
            if test_func():
                passed_tests += 1
                print(f"✅ {test_name} 测试通过")
            else:
                print(f"❌ {test_name} 测试失败")
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
    
    # 显示测试结果
    print("\n" + "=" * 60)
    print("📊 测试结果汇总")
    print(f"总测试数: {total_tests}")
    print(f"通过数: {passed_tests}")
    print(f"失败数: {total_tests - passed_tests}")
    
    if passed_tests == total_tests:
        print("🎉 所有测试都通过了！")
    else:
        print("⚠️ 部分测试失败")
    
    print("\n💡 注意:")
    print("- 实际下载测试需要有效的PowerBI连接")
    print("- 确保配置文件路径正确")
    print("- 检查Azure AD应用权限设置")


if __name__ == "__main__":
    main()
