"""
企业邮箱配置指南和解决方案
针对 Adidas 等企业邮箱的特殊配置
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart

def test_smtp_connection(smtp_server, port, email, password):
    """测试SMTP连接"""
    try:
        print(f"🔧 测试连接: {smtp_server}:{port}")
        context = ssl.create_default_context()
        
        with smtplib.SMTP(smtp_server, port) as server:
            print("🔒 启用TLS...")
            server.starttls(context=context)
            print(f"👤 测试登录: {email}")
            server.login(email, password)
            print("✅ 连接成功！")
            return True
            
    except Exception as e:
        print(f"❌ 连接失败: {str(e)}")
        return False

def try_different_smtp_configs():
    """尝试不同的SMTP配置"""
    email = "<EMAIL>"
    password = "Q_q<Lyf>[Yech]t,t"
    
    # 常见的企业邮箱SMTP配置
    smtp_configs = [
        ("smtp.office365.com", 587),
        ("smtp-mail.outlook.com", 587),
        ("outlook.office365.com", 587),
        ("smtp.outlook.com", 587),
        ("mail.adidas.com", 587),  # 可能的企业内部服务器
        ("smtp.adidas.com", 587),  # 可能的企业内部服务器
        ("exchange.adidas.com", 587),  # 可能的Exchange服务器
    ]
    
    print("🔍 尝试不同的SMTP服务器配置...")
    print("=" * 50)
    
    for smtp_server, port in smtp_configs:
        print(f"\n📡 尝试: {smtp_server}:{port}")
        if test_smtp_connection(smtp_server, port, email, password):
            print(f"🎉 找到可用配置: {smtp_server}:{port}")
            return smtp_server, port
        print("❌ 此配置不可用")
    
    print("\n⚠️ 所有标准配置都失败了")
    return None, None

def show_enterprise_solutions():
    """显示企业邮箱解决方案"""
    print("\n" + "=" * 60)
    print("🏢 企业邮箱解决方案指南")
    print("=" * 60)
    
    print("\n🔐 认证问题解决方案：")
    print("1. 应用密码 (App Password)")
    print("   - 登录 https://account.microsoft.com/security")
    print("   - 启用两步验证")
    print("   - 生成应用密码")
    print("   - 使用应用密码替代普通密码")
    
    print("\n2. OAuth2 认证")
    print("   - 企业级应用通常需要OAuth2")
    print("   - 需要在Azure AD中注册应用")
    print("   - 获取客户端ID和密钥")
    
    print("\n3. 企业内部SMTP服务器")
    print("   - 联系IT部门获取内部SMTP服务器地址")
    print("   - 可能是: mail.adidas.com 或 exchange.adidas.com")
    print("   - 可能需要VPN连接")
    
    print("\n📞 联系支持：")
    print("- Adidas IT Help Desk")
    print("- 企业邮件管理员")
    print("- 询问: 'Python SMTP邮件发送配置'")
    
    print("\n🔧 可能需要的信息：")
    print("- SMTP服务器地址和端口")
    print("- 认证方式 (密码/应用密码/OAuth2)")
    print("- 是否需要VPN")
    print("- 安全策略要求")

def create_oauth2_example():
    """创建OAuth2示例代码"""
    oauth2_code = '''
# OAuth2 认证示例 (需要额外的库)
# pip install msal requests-oauthlib

import msal
from email.mime.text import MIMEText
import requests

def get_access_token():
    """获取OAuth2访问令牌"""
    # 需要从Azure AD获取这些值
    client_id = "你的客户端ID"
    client_secret = "你的客户端密钥"
    tenant_id = "你的租户ID"
    
    app = msal.ConfidentialClientApplication(
        client_id,
        authority=f"https://login.microsoftonline.com/{tenant_id}",
        client_credential=client_secret,
    )
    
    result = app.acquire_token_for_client(scopes=["https://graph.microsoft.com/.default"])
    return result.get("access_token")

def send_email_with_graph_api():
    """使用Microsoft Graph API发送邮件"""
    access_token = get_access_token()
    
    headers = {
        'Authorization': f'Bearer {access_token}',
        'Content-Type': 'application/json'
    }
    
    email_data = {
        "message": {
            "subject": "测试邮件",
            "body": {
                "contentType": "Text",
                "content": "这是通过Graph API发送的邮件"
            },
            "toRecipients": [
                {"emailAddress": {"address": "收件人@example.com"}}
            ]
        }
    }
    
    url = "https://graph.microsoft.com/v1.0/me/sendMail"
    response = requests.post(url, headers=headers, json=email_data)
    
    if response.status_code == 202:
        print("✅ 邮件发送成功")
    else:
        print(f"❌ 发送失败: {response.text}")
'''
    
    print("\n" + "=" * 60)
    print("🔐 OAuth2 认证示例代码")
    print("=" * 60)
    print(oauth2_code)

def main():
    """主函数"""
    print("🏢 企业邮箱诊断工具")
    print("=" * 40)
    
    # 尝试不同的SMTP配置
    smtp_server, port = try_different_smtp_configs()
    
    if smtp_server:
        print(f"\n🎉 建议使用配置: {smtp_server}:{port}")
    else:
        print("\n❌ 标准SMTP配置都不可用")
        show_enterprise_solutions()
        create_oauth2_example()
    
    print("\n" + "=" * 60)
    print("📋 下一步建议：")
    print("1. 联系Adidas IT支持")
    print("2. 询问Python邮件发送的正确配置")
    print("3. 可能需要申请特殊权限")
    print("4. 考虑使用企业内部的邮件API")

if __name__ == "__main__":
    main()
