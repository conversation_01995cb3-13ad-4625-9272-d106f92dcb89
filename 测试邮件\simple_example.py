"""
简单的邮件发送示例
"""

from outlook_email_sender import OutlookEmailSender

def send_simple_email():
    """发送简单邮件的示例"""
    
    # 创建邮件发送器
    sender = OutlookEmailSender()
    
    # 配置信息 - 请替换为你的实际信息
    sender_email = "<EMAIL>"
    sender_password = "your_password"  # 建议使用应用密码
    recipient_emails = ["<EMAIL>"]
    
    # 发送文本邮件
    success = sender.send_email_smtp(
        sender_email=sender_email,
        sender_password=sender_password,
        recipient_emails=recipient_emails,
        subject="Python 测试邮件",
        body="这是一封通过 Python 发送的测试邮件！\n\n祝好！"
    )
    
    if success:
        print("邮件发送成功！")
    else:
        print("邮件发送失败！")

def send_html_email():
    """发送 HTML 邮件的示例"""
    
    sender = OutlookEmailSender()
    
    # 配置信息
    sender_email = "<EMAIL>"
    sender_password = "your_password"
    recipient_emails = ["<EMAIL>"]
    
    # HTML 邮件内容
    html_content = """
    <html>
        <head>
            <style>
                body { font-family: Arial, sans-serif; }
                .header { color: #0078d4; font-size: 24px; }
                .content { margin: 20px 0; }
                .footer { color: #666; font-size: 12px; }
            </style>
        </head>
        <body>
            <div class="header">欢迎使用 Python 邮件服务</div>
            <div class="content">
                <p>亲爱的用户，</p>
                <p>这是一封通过 Python 发送的 <strong>HTML 格式</strong> 邮件。</p>
                <ul>
                    <li>支持富文本格式</li>
                    <li>支持 CSS 样式</li>
                    <li>支持链接和图片</li>
                </ul>
                <p>访问我们的网站: <a href="https://www.microsoft.com">Microsoft</a></p>
            </div>
            <div class="footer">
                此邮件由 Python 自动发送，请勿回复。
            </div>
        </body>
    </html>
    """
    
    # 发送 HTML 邮件
    success = sender.send_email_smtp(
        sender_email=sender_email,
        sender_password=sender_password,
        recipient_emails=recipient_emails,
        subject="HTML 格式测试邮件",
        body=html_content,
        is_html=True,
        cc_emails=["<EMAIL>"]  # 可选：抄送
    )
    
    if success:
        print("HTML 邮件发送成功！")
    else:
        print("HTML 邮件发送失败！")

def send_email_with_attachment():
    """发送带附件的邮件示例"""
    
    sender = OutlookEmailSender()
    
    # 配置信息
    sender_email = "<EMAIL>"
    sender_password = "your_password"
    recipient_emails = ["<EMAIL>"]
    
    # 创建一个示例文件作为附件
    with open("test_attachment.txt", "w", encoding="utf-8") as f:
        f.write("这是一个测试附件文件。\n")
        f.write("文件创建时间: 2024年\n")
        f.write("内容: Python 邮件发送测试")
    
    # 发送带附件的邮件
    success = sender.send_email_smtp(
        sender_email=sender_email,
        sender_password=sender_password,
        recipient_emails=recipient_emails,
        subject="带附件的测试邮件",
        body="请查看附件中的测试文件。",
        attachments=["test_attachment.txt"]
    )
    
    if success:
        print("带附件的邮件发送成功！")
    else:
        print("带附件的邮件发送失败！")

if __name__ == "__main__":
    print("选择要执行的示例:")
    print("1. 发送简单文本邮件")
    print("2. 发送 HTML 邮件")
    print("3. 发送带附件的邮件")
    
    choice = input("请输入选择 (1-3): ")
    
    if choice == "1":
        send_simple_email()
    elif choice == "2":
        send_html_email()
    elif choice == "3":
        send_email_with_attachment()
    else:
        print("无效选择！")
