#!/usr/bin/env python3
from pbi_downloader_class import PowerBIDownloader
import json
from datetime import datetime


def test_inventory_download():
    try:
        # 创建下载器实例
        downloader = PowerBIDownloader()
        
        # 库存报表的JSON参数
        json_data = {
            "report_name": "TW Inventory",
            "dim": "Snapshot Date,Article No",
            "fcst": "Total Stock Qty",
            "Snapshot_date": ["2025/7/8"],
            "article_no": ["All"],
            "stock_category": ["All"]
        }
        
        # 执行下载
        print("🚀 开始下载...")
        result = downloader.download_data(json_data)
        
        print("✅ 库存报表下载测试成功！")
        print(f"📊 结果: {result}")
        return True
        
    except Exception as e:
        print(f"❌ 库存报表下载测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    success = test_inventory_download()

    print("\n" + "=" * 60)
    if success:
        print("🎉 库存报表下载测试完成！")
    else:
        print("❌ 库存报表下载测试失败！")


if __name__ == "__main__":
    main()
