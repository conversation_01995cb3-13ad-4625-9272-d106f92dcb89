# Outlook 365 邮件发送工具

这是一个完整的 Python 工具包，用于通过 Outlook 365 发送邮件。支持两种方式：SMTP 和 Microsoft Graph API。

## 功能特性

- ✅ 支持 SMTP 方式发送邮件（适用于个人账户）
- ✅ 支持 Microsoft Graph API（适用于企业账户）
- ✅ 支持 HTML 和纯文本邮件
- ✅ 支持附件发送
- ✅ 支持抄送（CC）和密送（BCC）
- ✅ 完整的错误处理
- ✅ 类型提示支持

## 安装依赖

```bash
pip install -r requirements.txt
```

## 快速开始

### 方法1: SMTP 方式（推荐用于个人账户）

```python
from outlook_email_sender import OutlookEmailSender

sender = OutlookEmailSender()

# 发送简单邮件
sender.send_email_smtp(
    sender_email="<EMAIL>",
    sender_password="your_password",
    recipient_emails=["<EMAIL>"],
    subject="测试邮件",
    body="这是一封测试邮件"
)
```

### 方法2: Microsoft Graph API（推荐用于企业账户）

```python
from outlook_email_sender import OutlookGraphAPI

graph_sender = OutlookGraphAPI(
    tenant_id="your-tenant-id",
    client_id="your-client-id", 
    client_secret="your-client-secret"
)

graph_sender.send_email_graph(
    sender_email="<EMAIL>",
    recipient_emails=["<EMAIL>"],
    subject="测试邮件",
    body="这是一封测试邮件"
)
```

## 配置说明

### SMTP 配置

1. **获取应用密码**（推荐）:
   - 登录 Outlook.com
   - 进入"安全性"设置
   - 启用"两步验证"
   - 生成"应用密码"用于 Python 脚本

2. **直接使用账户密码**:
   - 需要在 Outlook 设置中启用"不太安全的应用访问"

### Graph API 配置

1. **注册 Azure AD 应用**:
   - 访问 [Azure Portal](https://portal.azure.com)
   - 进入"Azure Active Directory" > "应用注册"
   - 创建新应用注册
   - 记录"应用程序 ID"和"目录 ID"

2. **配置 API 权限**:
   - 添加 Microsoft Graph 权限
   - 选择"Mail.Send"权限
   - 管理员同意权限

3. **创建客户端密钥**:
   - 在应用设置中创建新的客户端密钥
   - 记录密钥值

## 使用示例

### 发送 HTML 邮件

```python
html_body = """
<html>
    <body>
        <h2>欢迎</h2>
        <p>这是一封 <b>HTML</b> 邮件</p>
    </body>
</html>
"""

sender.send_email_smtp(
    sender_email="<EMAIL>",
    sender_password="your_password",
    recipient_emails=["<EMAIL>"],
    subject="HTML 邮件",
    body=html_body,
    is_html=True
)
```

### 发送带附件的邮件

```python
sender.send_email_smtp(
    sender_email="<EMAIL>",
    sender_password="your_password",
    recipient_emails=["<EMAIL>"],
    subject="带附件的邮件",
    body="请查看附件",
    attachments=["file1.pdf", "file2.docx"]
)
```

### 发送给多个收件人

```python
sender.send_email_smtp(
    sender_email="<EMAIL>",
    sender_password="your_password",
    recipient_emails=["<EMAIL>", "<EMAIL>"],
    subject="群发邮件",
    body="这是一封群发邮件",
    cc_emails=["<EMAIL>"],
    bcc_emails=["<EMAIL>"]
)
```

## 文件说明

- `outlook_email_sender.py` - 主要的邮件发送类
- `simple_example.py` - 简单使用示例
- `config_example.py` - 配置文件示例
- `requirements.txt` - 依赖包列表

## 注意事项

1. **安全性**: 
   - 不要在代码中硬编码密码
   - 使用环境变量或配置文件存储敏感信息
   - 推荐使用应用密码而不是账户密码

2. **限制**:
   - Outlook.com 有发送频率限制
   - 企业账户可能有额外的安全策略

3. **错误处理**:
   - 代码包含完整的异常处理
   - 检查返回值确认发送状态

## 故障排除

### 常见错误

1. **认证失败**:
   - 检查邮箱地址和密码
   - 确认已启用应用密码或不太安全的应用访问

2. **连接超时**:
   - 检查网络连接
   - 确认防火墙设置

3. **权限错误**:
   - 检查 Graph API 权限配置
   - 确认管理员已同意权限

## 许可证

MIT License
