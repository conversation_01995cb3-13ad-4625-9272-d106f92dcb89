#!/usr/bin/env python3
"""
PowerBI 数据下载器类
将原有的函数式代码重构为面向对象的类结构
"""

from datetime import datetime, timedelta
import pandas as pd
import os
import json
import platform
import msal
from concurrent.futures import ThreadPoolExecutor, as_completed
import hashlib
import win32com.client 
import pythoncom
import traceback
import gc
import csv
from typing import Optional, Tuple, List, Dict
import time
import uuid


class PowerBIDownloader:
    """PowerBI 数据下载器类"""
    
    def __init__(self, client_id=None, client_secret=None, tenant_id=None, workspace_name=None, base_dir=None):
        """
        初始化 PowerBI 下载器
        
        Args:
            client_id: Azure AD 应用客户端 ID
            client_secret: Azure AD 应用客户端密钥
            tenant_id: Azure AD 租户 ID
            workspace_name: PowerBI 工作区名称
            base_dir: 配置文件基础目录
        """
        # PowerBI REST API配置
        self.client_id = client_id or "ea0616ba-638b-4df5-95b9-636659ae5121"
        self.client_secret = client_secret or "****************************************"
        self.tenant_id = tenant_id or "3bfeb222-e42c-4535-aace-ea6f7751369b"
        self.workspace_name = workspace_name or "DNA GCA_DEV:TW MAJOR REPORT"
        
        # 配置文件的地址
        self.base_dir = base_dir or "C:/Users/<USER>/OneDrive - adidas/Desktop/code"
        
        # 时间维度映射
        self.TIME_DIMENSION_MAP = {
            "Snapshot Date": "date",
            "DATE": "date",
            "MONTH": "month",
            "QUARTER": "quarter",
            "YEAR": "year"
        }
        
        # 临时目录配置
        self.TEMP_DIR = "temp_batches_practical"
        self.custom_dir = self._get_default_download_dir()
        self.temp_dir = os.path.join(self.custom_dir, self.TEMP_DIR)
        
        # 确保目录存在
        os.makedirs(self.custom_dir, exist_ok=True)
        os.makedirs(self.temp_dir, exist_ok=True)
        
        # 加载配置
        self.practical_config_list = self._load_json_mapping('practical_config.json')
        
        print(f"✅ PowerBI 下载器初始化完成")
        print(f"📁 下载目录: {self.custom_dir}")
        print(f"🏢 工作区: {self.workspace_name}")
    
    def _get_default_download_dir(self):
        """获取默认下载目录"""
        system = platform.system()
        if system == "Windows":
            return os.path.join(os.path.expanduser("~"), "Desktop", "测试", "实用优化下载")
        elif system == "Darwin":  # macOS
            return os.path.join(os.path.expanduser("~"), "Desktop", "PowerBI实用优化下载")
        else:  # Linux
            return os.path.join(os.path.expanduser("~"), "Downloads", "PowerBI实用优化下载")
    
    def _load_json_mapping(self, filename):
        """加载JSON映射文件"""
        file_path = os.path.join(self.base_dir, filename)
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def get_access_token(self):
        """获取访问令牌 - 弹框登录模式"""
        try:
            authority = "https://login.microsoftonline.com/common"
            app = msal.PublicClientApplication(client_id=self.client_id, authority=authority)
            scopes = ["https://analysis.windows.net/powerbi/api/.default"]
            
            print("🔄 正在获取token...")
            result = app.acquire_token_interactive(scopes=scopes)
            
            if "access_token" in result:
                token = result["access_token"]
                print(f"📅 Token过期时间: {result.get('expires_in', 'Unknown')}秒")
                return token
            else:
                error_msg = result.get("error_description", "Unknown error")
                print(f"❌ 获取token失败: {error_msg}")
                raise Exception(f"Failed to get token: {error_msg}")
        except Exception as e:
            print(f"❌ 获取access_token异常: {e}")
            raise
    
    def get_mapping_files(self, name):
        """根据报表名称获取映射文件名"""
        if name == "TW Inventory":
            return ('dim_mapping_inventory.json', 
                   'fcst_mapping_inventory.json', 
                   'filter_mapping_inventory.json')
        elif name == "TW Transaction Template":
            return ('dim_mapping_transaction.json', 
                   'fcst_mapping_transaction.json', 
                   'filter_mapping_transaction.json')
        else:
            raise ValueError(f"未知的name参数: {name}")
    
    def load_all_mappings(self, name):
        """加载所有映射文件"""
        dim_file, fcst_file, filter_file = self.get_mapping_files(name)
        dim_mapping = self._load_json_mapping(dim_file)
        fcst_mapping = self._load_json_mapping(fcst_file)
        filter_mapping = self._load_json_mapping(filter_file)
        return dim_mapping, fcst_mapping, filter_mapping
    
    def build_dim_actual_names(self, dim_mapping):
        """构建维度实际名称映射"""
        dim_actual_names = {}
        for dim, expr in dim_mapping.items():
            table_part, column_part = expr.split('[', 1)
            table_name = table_part.strip("'")
            column_name = column_part.rstrip(']')
            actual_name = f"{table_name}[{column_name}]"
            dim_actual_names[dim] = actual_name
        return dim_actual_names
    
    def build_fcst_actual_names(self, fcst_mapping):
        """构建指标实际名称映射"""
        fcst_actual_names = {}
        for fcst, expr in fcst_mapping.items():
            if '[' in expr and ']' in expr:
                start = expr.find('[') + 1
                end = expr.find(']', start)
                if end != -1:
                    metric_name = expr[start:end]
                    actual_name = f"[{metric_name}]"
                else:
                    actual_name = expr.split(',')[0].strip('"')
            else:
                actual_name = expr.split(',')[0].strip('"')
            fcst_actual_names[fcst] = actual_name
        return fcst_actual_names
    
    def get_quarter_start_end(self, year, quarter):
        """根据自然天计算季度的开始和结束日期"""
        start_month = (quarter - 1) * 3 + 1
        end_month = start_month + 2
        start_date = datetime(year, start_month, 1)
        
        if end_month == 12:
            end_date = datetime(year+1, 1, 1) - timedelta(days=1)
        else:
            end_date = datetime(year, end_month+1, 1) - timedelta(days=1)
        
        return start_date, end_date
    
    def get_quarter(self, dt):
        """获取日期所在的季度"""
        return (dt.month - 1) // 3 + 1
    
    def get_practical_config(self, batch_type, total_units):
        """根据时间维度和时间跨度获取实用优化配置"""
        for config in self.practical_config_list:
            if config[0] == batch_type and config[1] <= total_units <= config[2]:
                return config[3], config[4]
        
        # 如果没有匹配项，返回默认配置
        default_config = {
            'date': (5, 10),
            'month': (5, 10),
            'quarter': (5, 10),
            'year': (1, 1)
        }
        return default_config.get(batch_type, (10, 10))
    
    def calculate_and_configure_batch(self, batch_type, start_date, end_date):
        """计算时间跨度并配置优化参数"""
        if batch_type == 'date':
            total_units = (end_date - start_date).days + 1
            unit_name = "天"
        elif batch_type == 'month':
            total_units = (end_date.year - start_date.year) * 12 + (end_date.month - start_date.month) + 1
            unit_name = "个月"
        elif batch_type == 'quarter':
            start_quarter = (start_date.month - 1) // 3 + 1
            end_quarter = (end_date.month - 1) // 3 + 1
            total_units = (end_date.year - start_date.year) * 4 + (end_quarter - start_quarter) + 1
            unit_name = "个季度"
        elif batch_type == 'year':
            total_units = end_date.year - start_date.year + 1
            unit_name = "年"
        else:
            total_units = (end_date - start_date).days + 1
            unit_name = "天"
            batch_type = 'date'
        
        print(f"📅📅 时间跨度: {total_units} {unit_name}")
        
        # 获取优化配置
        PRACTICAL_BATCH, PRACTICAL_CONCURRENT = self.get_practical_config(batch_type, total_units)
        
        print(f"⚡⚡ 实用优化配置: {PRACTICAL_BATCH}{unit_name.replace('个', '')}批次 | {PRACTICAL_CONCURRENT}并发")
        
        return total_units, unit_name, PRACTICAL_BATCH, PRACTICAL_CONCURRENT

    def build_dax_filter(self, filter_mapping, **kwargs):
        """构建DAX过滤条件"""
        filter_clauses = []
        for key, value in kwargs.items():
            if value and key in filter_mapping:
                dax_field = filter_mapping[key]
                if isinstance(value, list):
                    value_set = "{" + ",".join([f'\"{v}\"' for v in value]) + "}"
                    filter_clauses.append(f"{dax_field} in {value_set}")
                else:
                    filter_clauses.append(f"{dax_field} = \"{value}\"")
        if filter_clauses:
            return ",\n                " + ",\n                ".join(filter_clauses)
        return ""

    def build_practical_dax(self, dim_list, fcst_list, start_date, end_date,
                           dim_mapping, fcst_mapping, pbi_name, dax_filter=""):
        """拼接DAX查询"""
        if not dim_list or not fcst_list:
            raise ValueError("维度和指标参数不能为空")

        # 构建维度字段列表
        dim_fields = []
        for dim in dim_list:
            if dim not in dim_mapping:
                raise ValueError(f"无效的维度字段: {dim}")
            dim_fields.append(dim_mapping[dim])

        # 构建指标字段列表
        fcst_fields = []
        for fcst in fcst_list:
            if fcst not in fcst_mapping:
                raise ValueError(f"无效的指标字段: {fcst}")
            fcst_fields.append(fcst_mapping[fcst])

        try:
            # 解析日期
            start_parts = start_date.split(',')
            end_parts = end_date.split(',')

            if len(start_parts) != 3 or len(end_parts) != 3:
                raise ValueError("日期格式错误，应为 YYYY,MM,DD")

            if pbi_name == "TW Inventory":
                date_table = "'Dim_date'[snapshot_date]"
            elif pbi_name == "TW Transaction Template":
                date_table="'vw_dim_calendar'[calendar_day]"
            else:
                raise ValueError(f"未知的报表名称: {pbi_name}")

            dax_query = f"""EVALUATE
                CALCULATETABLE(
                    SUMMARIZECOLUMNS(
                        {", ".join(dim_fields)},
                        {", ".join(fcst_fields)}
                    ),
                    DATESBETWEEN(
                        {date_table},
                        DATE({start_parts[0]}, {start_parts[1]}, {start_parts[2]}),
                        DATE({end_parts[0]}, {end_parts[1]}, {end_parts[2]})
                    ){dax_filter}
                )"""

            return dax_query
        except Exception as e:
            print(f"❌ DAX查询构建失败: {e}")
            raise

    def parse_str_to_list(self, val):
        """将字符串转为列表"""
        if val is None:
            return None
        if isinstance(val, list):
            if len(val) == 1 and val[0] == "All":
                return None
            return val
        if isinstance(val, str):
            try:
                result = json.loads(val)
                if isinstance(result, list):
                    if len(result) == 1 and result[0] == "All":
                        return None
                    return result
                else:
                    if result == "All":
                        return None
                    return [result]
            except Exception:
                if val == "All":
                    return None
                return [val]
        return [val]

    def get_file_stats(self, file_path):
        """同时获取行数和列数"""
        row_count, col_count = 0, 0
        with open(file_path, 'r', encoding='utf-8-sig') as f:
            reader = csv.reader(f)
            for i, row in enumerate(reader):
                if i == 0:  # 首行获取列数
                    col_count = len(row)
                row_count = i  # 末行索引=实际行数
        return row_count, col_count

    def cleanup_temp_files(self, request_uuid):
        """清理本次请求的临时文件"""
        try:
            import glob
            pattern = os.path.join(self.temp_dir, f"batch_{request_uuid}_*.csv")
            temp_files = glob.glob(pattern)
            removed_count = 0

            for temp_file in temp_files:
                try:
                    os.remove(temp_file)
                    removed_count += 1
                except:
                    pass

            print(f"🧹 清理临时文件: {removed_count} 个文件已删除")

        except Exception as e:
            print(f"⚠️  清理临时文件时出现异常: {e}")

    def download_data(self, json_data):

        try:
            timestamp1 = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f"\n{'='*20} PowerBI 实用优化模式 {'='*20}")
            print(f"开始时间: {timestamp1}")

            if not dim_params or not fcst_params:
                raise ValueError("缺少必要参数: dim_params, fcst_params")

            # 动态加载映射和实际名称
            dim_mapping, fcst_mapping, filter_mapping = self.load_all_mappings(report_name)
            dim_actual_names = self.build_dim_actual_names(dim_mapping)
            fcst_actual_names = self.build_fcst_actual_names(fcst_mapping)

            # 解析所有筛选参数
            filter_param_names = [
                'launch', 'pro_type', 'pro_div', 'pro_article', 'pro_category', 'pro_yeezy',
                'sto_code', 'sto_status', 'sto_main_channel', 'sto_format', 'sto_type',
                'article_no', 'stock_category', 'global_store_code', 'store_type',
                'stock_position', 'inv_owner'
            ]
            filter_param_values = {}
            for param in filter_param_names:
                if param in filter_params:
                    filter_param_values[param] = self.parse_str_to_list(filter_params[param])

            # 生成 DAX filter 字符串
            dax_filter = self.build_dax_filter(filter_mapping, **filter_param_values)

            if report_name == "TW Inventory":
                if not snapshot_dates:
                    raise ValueError("库存报表需要提供snapshot_dates参数")

                print(f"🔄 库存报表处理模式 | 快照日期: {len(snapshot_dates)}个")

                # 生成批次（每个日期作为一个批次）
                batches, PRACTICAL_CONCURRENT, request_uuid = self.generate_inventory_date_batches(snapshot_dates)
                PRACTICAL_BATCH = 1
                batch_type = 'date'

                # 使用自定义的批次列表
                file_path, row_count, col_count, file_size_mb, time_str = self.fetch_practical_data(
                    dim_params, fcst_params, "", "", batch_type, PRACTICAL_BATCH, PRACTICAL_CONCURRENT,
                    dim_mapping, fcst_mapping, dim_actual_names, fcst_actual_names, report_name,
                    dax_filter=dax_filter, custom_batches=batches, request_uuid=request_uuid
                )
            else:
                # 转换日期格式
                date_min = date_min.replace("/", ",")
                date_max = date_max.replace("/", ",")

                print("开始日期：", date_min)
                print("结束日期：", date_max)

                # 计算预期数据量
                start_parts = date_min.split(',')
                end_parts = date_max.split(',')
                start_date = datetime(int(start_parts[0]), int(start_parts[1]), int(start_parts[2]))
                end_date = datetime(int(end_parts[0]), int(end_parts[1]), int(end_parts[2]))

                # 按优先级检测时间维度字段
                batch_type = next(
                    (self.TIME_DIMENSION_MAP[field] for field in ["Snapshot Date","DATE", "MONTH", "QUARTER", "YEAR"]
                     if field in dim_params),
                    None
                )

                print(f"🔍 分批策略: {batch_type}")

                # 计算时间跨度和配置
                total_units, unit_name, PRACTICAL_BATCH, PRACTICAL_CONCURRENT = self.calculate_and_configure_batch(
                    batch_type, start_date, end_date
                )

                # 获取数据并生成CSV文件
                file_path, row_count, col_count, file_size_mb, time_str = self.fetch_practical_data(
                    dim_params, fcst_params, date_min, date_max, batch_type, PRACTICAL_BATCH, PRACTICAL_CONCURRENT,
                    dim_mapping, fcst_mapping, dim_actual_names, fcst_actual_names, report_name,
                    dax_filter=dax_filter
                )

            # 计算运行时间
            timestamp2 = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            print(f"\n{'='*50}")
            print(f"🎉 下载完成: {timestamp2}")
            print(f"⏱️ 总运行时长: {time_str}")
            print(f"📊 最终数据: {row_count:,}行, {col_count}列")
            print(f"💾 CSV文件大小: {file_size_mb} MB")
            print(f"📁 文件路径: {file_path}")
            print(f"💻 操作系统: {platform.system()}")
            print(f"{'='*50}")

            return file_path

        except Exception as e:
            error_msg = f"实用优化下载失败: {str(e)}"
            print(f"❌❌❌❌ {error_msg}")
            raise
