"""
Outlook 365 邮件发送工具
支持两种方式：Microsoft Graph API 和 SMTP
"""

import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
import os
from typing import List, Optional
import requests
import json


class OutlookEmailSender:
    """Outlook 365 邮件发送类"""
    
    def __init__(self):
        self.smtp_server = "smtp-mail.outlook.com"
        self.smtp_port = 587
        
    def send_email_smtp(self, 
                       sender_email: str,
                       sender_password: str,
                       recipient_emails: List[str],
                       subject: str,
                       body: str,
                       is_html: bool = False,
                       attachments: Optional[List[str]] = None,
                       cc_emails: Optional[List[str]] = None,
                       bcc_emails: Optional[List[str]] = None):
        """
        使用 SMTP 发送邮件
        
        Args:
            sender_email: 发件人邮箱
            sender_password: 发件人密码或应用密码
            recipient_emails: 收件人邮箱列表
            subject: 邮件主题
            body: 邮件正文
            is_html: 是否为HTML格式
            attachments: 附件文件路径列表
            cc_emails: 抄送邮箱列表
            bcc_emails: 密送邮箱列表
        """
        try:
            # 创建邮件对象
            message = MIMEMultipart()
            message["From"] = sender_email
            message["To"] = ", ".join(recipient_emails)
            message["Subject"] = subject
            
            if cc_emails:
                message["Cc"] = ", ".join(cc_emails)
            
            # 添加邮件正文
            if is_html:
                message.attach(MIMEText(body, "html", "utf-8"))
            else:
                message.attach(MIMEText(body, "plain", "utf-8"))
            
            # 添加附件
            if attachments:
                for file_path in attachments:
                    if os.path.isfile(file_path):
                        with open(file_path, "rb") as attachment:
                            part = MIMEBase('application', 'octet-stream')
                            part.set_payload(attachment.read())
                        
                        encoders.encode_base64(part)
                        part.add_header(
                            'Content-Disposition',
                            f'attachment; filename= {os.path.basename(file_path)}'
                        )
                        message.attach(part)
            
            # 准备收件人列表
            all_recipients = recipient_emails.copy()
            if cc_emails:
                all_recipients.extend(cc_emails)
            if bcc_emails:
                all_recipients.extend(bcc_emails)
            
            # 创建安全连接并发送邮件
            context = ssl.create_default_context()
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls(context=context)
                server.login(sender_email, sender_password)
                server.sendmail(sender_email, all_recipients, message.as_string())
            
            print(f"邮件发送成功！收件人: {', '.join(recipient_emails)}")
            return True
            
        except Exception as e:
            print(f"邮件发送失败: {str(e)}")
            return False


class OutlookGraphAPI:
    """使用 Microsoft Graph API 发送邮件"""
    
    def __init__(self, tenant_id: str, client_id: str, client_secret: str):
        """
        初始化 Graph API 客户端
        
        Args:
            tenant_id: Azure AD 租户 ID
            client_id: 应用程序 ID
            client_secret: 应用程序密钥
        """
        self.tenant_id = tenant_id
        self.client_id = client_id
        self.client_secret = client_secret
        self.access_token = None
        
    def get_access_token(self):
        """获取访问令牌"""
        url = f"https://login.microsoftonline.com/{self.tenant_id}/oauth2/v2.0/token"
        
        data = {
            'grant_type': 'client_credentials',
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'scope': 'https://graph.microsoft.com/.default'
        }
        
        response = requests.post(url, data=data)
        if response.status_code == 200:
            self.access_token = response.json()['access_token']
            return True
        else:
            print(f"获取访问令牌失败: {response.text}")
            return False
    
    def send_email_graph(self,
                        sender_email: str,
                        recipient_emails: List[str],
                        subject: str,
                        body: str,
                        is_html: bool = False,
                        cc_emails: Optional[List[str]] = None,
                        bcc_emails: Optional[List[str]] = None):
        """
        使用 Graph API 发送邮件
        """
        if not self.access_token and not self.get_access_token():
            return False
        
        url = f"https://graph.microsoft.com/v1.0/users/{sender_email}/sendMail"
        
        headers = {
            'Authorization': f'Bearer {self.access_token}',
            'Content-Type': 'application/json'
        }
        
        # 构建收件人列表
        to_recipients = [{"emailAddress": {"address": email}} for email in recipient_emails]
        cc_recipients = [{"emailAddress": {"address": email}} for email in (cc_emails or [])]
        bcc_recipients = [{"emailAddress": {"address": email}} for email in (bcc_emails or [])]
        
        # 构建邮件数据
        email_data = {
            "message": {
                "subject": subject,
                "body": {
                    "contentType": "HTML" if is_html else "Text",
                    "content": body
                },
                "toRecipients": to_recipients,
                "ccRecipients": cc_recipients,
                "bccRecipients": bcc_recipients
            }
        }
        
        response = requests.post(url, headers=headers, json=email_data)
        
        if response.status_code == 202:
            print(f"邮件发送成功！收件人: {', '.join(recipient_emails)}")
            return True
        else:
            print(f"邮件发送失败: {response.text}")
            return False


def main():
    """示例用法"""
    
    # 方法1: 使用 SMTP (推荐用于个人账户)
    print("=== SMTP 方式发送邮件 ===")
    smtp_sender = OutlookEmailSender()
    
    # 配置邮件信息
    sender_email = "<EMAIL>"  # 替换为你的邮箱
    sender_password = "your_password"        # 替换为你的密码或应用密码
    recipient_emails = ["<EMAIL>"]  # 替换为收件人邮箱
    
    # 发送简单文本邮件
    smtp_sender.send_email_smtp(
        sender_email=sender_email,
        sender_password=sender_password,
        recipient_emails=recipient_emails,
        subject="测试邮件 - SMTP",
        body="这是一封通过 Python SMTP 发送的测试邮件。"
    )
    
    # 发送 HTML 邮件
    html_body = """
    <html>
        <body>
            <h2>HTML 邮件测试</h2>
            <p>这是一封 <b>HTML 格式</b> 的邮件。</p>
            <ul>
                <li>支持富文本格式</li>
                <li>支持链接: <a href="https://www.microsoft.com">Microsoft</a></li>
            </ul>
        </body>
    </html>
    """
    
    smtp_sender.send_email_smtp(
        sender_email=sender_email,
        sender_password=sender_password,
        recipient_emails=recipient_emails,
        subject="HTML 邮件测试 - SMTP",
        body=html_body,
        is_html=True,
        cc_emails=["<EMAIL>"],  # 可选：抄送
        # attachments=["path/to/file.pdf"]  # 可选：附件
    )
    
    # 方法2: 使用 Microsoft Graph API (推荐用于企业账户)
    print("\n=== Graph API 方式发送邮件 ===")
    
    # 需要在 Azure AD 中注册应用并获取以下信息
    tenant_id = "your-tenant-id"
    client_id = "your-client-id"
    client_secret = "your-client-secret"
    
    graph_sender = OutlookGraphAPI(tenant_id, client_id, client_secret)
    
    graph_sender.send_email_graph(
        sender_email="<EMAIL>",
        recipient_emails=["<EMAIL>"],
        subject="测试邮件 - Graph API",
        body="这是一封通过 Microsoft Graph API 发送的测试邮件。",
        is_html=False
    )


if __name__ == "__main__":
    main()
