{"Net Qty (Sell-Out)": "\"Net Qty (Sell-Out)\", [<PERSON> <PERSON><PERSON> (Sell-Out)]", "Net Qty (Sell-Out) LY": "\"Net Qty (Sell-Out) LY\", [<PERSON> Qty (Sell-Out) LY]", "Net Qty (Finance)": "\"Net Qty (Finance)\", [Net Qty (Finance)]", "Net Qty (Finance) LY": "\"Net Qty (Finance) LY\", [Net Qty (Finance) LY]", "Net Sales (Sell-Out Excl. VAT)": "\"Net Sales (Sell-Out, Excl. VAT)\", [Net Sales (Sell-Out, Excl. VAT)]", "Net Sales (Sell-Out Excl. VAT) LY": "\"Net Sales (Sell-Out, Excl. VAT) LY\", [Net Sales (Sell-Out, Excl. VAT) LY]", "Net Sales (Sell-Out Incl. VAT)": "\"Net Sales (Sell-Out, Incl. VAT)\", [Net Sales (Sell-Out, Incl. VAT)]", "Net Sales (Sell-Out Incl. VAT) LY": "\"Net Sales (Sell-Out, Incl. VAT) LY\", [Net Sales (Sell-Out, Incl. VAT) LY]", "Net Sales (Finance Excl. VAT)": "\"Net Sales (Finance, Excl. VAT)\", [Net Sales (Finance, Excl. VAT)]", "Net Sales (Finance Excl. VAT) LY": "\"Net Sales (Finance, Excl. VAT) LY\", [Net Sales (Finance, Excl. VAT) LY]", "Net Sales (Finance Incl. VAT)": "\"Net Sales (Finance, Incl. VAT)\", [Net Sales (Finance, Incl. VAT)]", "Net Sales (Finance Incl. VAT) LY": "\"Net Sales (Finance, Incl. VAT) LY\", [Net Sales (Finance, Incl. VAT) LY]", "Gross Sales (Sell-Out Excl. VAT)": "\"Gross Sales (Sell-Out, Excl. VAT)\", [Gross Sales (Sell-Out, Excl. VAT)]", "Gross Sales (Sell-Out Excl. VAT) LY": "\"Gross Sales (Sell-Out, Excl. VAT) LY\", [Gross Sales (Sell-Out, Excl. VAT) LY]", "Gross Sales (Sell-Out Incl. VAT)": "\"Gross Sales (Sell-Out, Incl. VAT)\", [Gross Sales (Sell-Out, Incl. VAT)]", "Gross Sales (Sell-Out Incl. VAT) LY": "\"Gross Sales (Sell-Out, Incl. VAT) LY\", [Gross Sales (Sell-Out, Incl. VAT) LY]", "Nr. of Sales Transactions": "\"Nr. of Sales Transactions\", [Nr. of Sales Transactions]", "Nr. of Sales Transactions LY": "\"Nr. of Sales Transactions LY\", [Nr. of Sales Transactions LY]", "Nr. of Stores (Sales)": "\"Nr. of Stores (Sales)\", [Nr. of Stores (Sales)]", "Nr. of Stores (Sales) LY": "\"Nr. of Stores (Sales) LY\", [Nr. of Stores (Sales) LY]", "Nr. of Stores (Traffic)": "\"Nr. of Stores (Traffic)\", [Nr. of Stores (Traffic)]", "Nr. of Stores (Traffic) LY": "\"Nr. of Stores (Traffic) LY\", [Nr. of Stores (Traffic) LY]", "Traffic Count": "\"Traffic Count\", [Traffic Count]", "Traffic Count LY": "\"Traffic Count LY\", [Traffic Count LY]", "Nr. Of Days (Traffic)": "\"Nr. Of Days (Traffic)\", [Nr. Of Days (Traffic)]", "Nr. Of Days (Traffic) LY": "\"Nr. Of Days (Traffic) LY\", [Nr. Of Days (Traffic) LY]", "Average Daily Traffic per Store": "\"Average Daily Traffic per Store\", [Average Daily Traffic per Store]", "Average Daily Traffic per Store LY": "\"Average Daily Traffic per Store LY\", [Average Daily Traffic per Store LY]", "Average Traffic per Store": "\"Average Traffic per Store\", [Average Traffic per Store]", "Average Traffic per Store LY": "\"Average Traffic per Store LY\", [Average Traffic per Store LY]", "Conversion Rate": "\"Conversion Rate\", [Conversion Rate]", "Conversion Rate LY": "\"Conversion Rate LY\", [Conversion Rate LY]", "ASP (Sell-Out Excl. VAT)": "\"ASP (Sell-Out, Excl. VAT)\", [ASP (Sell-Out, Excl. VAT)]", "ASP (Sell-Out Excl. VAT) LY": "\"ASP (Sell-Out, Excl. VAT) LY\", [ASP (Sell-Out, Excl. VAT) LY]", "ASP (Sell-Out Incl. VAT)": "\"ASP (Sell-Out, Incl. VAT)\", [ASP (Sell-Out, Incl. VAT)]", "ASP (Sell-Out Incl. VAT) LY": "\"ASP (Sell-Out, Incl. VAT) LY\", [ASP (Sell-Out, Incl. VAT) LY]", "ATV (Excl. VAT)": "\"ATV (Excl. VAT)\", [ATV (Excl. VAT)]", "ATV (Excl. VAT) LY": "\"ATV (Excl. VAT) LY\", [ATV (Excl. VAT) LY]", "ATV (Incl. VAT)": "\"ATV (Incl. VAT)\", [ATV (Incl. VAT)]", "ATV (Incl. VAT) LY": "\"ATV (Incl. VAT) LY\", [ATV (Incl. VAT) LY]", "UPT": "\"UPT\", [UPT]", "UPT LY": "\"UPT LY\", [UPT LY]", "MD%": "\"MD%\", [MD%]", "MD%LY": "\"MD%LY\", [MD%LY]", "Net Qty (FP)": "\"Net Qty (FP)\", [Net Qty (FP)]", "Net Qty (FP) LY": "\"Net Qty (FP) LY\", [Net Qty (FP) LY]", "Net Sales (FP Excl. VAT)": "\"Net Sales (FP, Excl. VAT)\", [Net Sales (FP, Excl. VAT)]", "Net Sales (FP Excl. VAT) LY": "\"Net Sales (FP, Excl. VAT) LY\", [Net Sales (FP, Excl. VAT) LY]", "Net Sales (FP Incl. VAT)": "\"Net Sales (FP, Incl. VAT)\", [Net Sales (FP, Incl. VAT)]", "Net Sales (FP Incl. VAT) LY": "\"Net Sales (FP, Incl. VAT) LY\", [Net Sales (FP, Incl. VAT) LY]", "FP Share by Value (Excl. VAT)": "\"FP Share by Value (Excl. VAT)\", [FP Share by Value (Excl. VAT)]", "FP Share by Value (Excl. VAT) LY": "\"FP Share by Value (Excl. VAT) LY\", [FP Share by Value (Excl. VAT) LY]", "FP Share by Value (Incl. VAT)": "\"FP Share by Value (Incl. VAT)\", [FP Share by Value (Incl. VAT)]", "FP Share by Value (Incl. VAT) LY": "\"FP Share by Value (Incl. VAT) LY\", [FP Share by Value (Incl. VAT) LY]", "FP Share by Volume": "\"FP Share by Volume\", [FP Share by Volume]", "FP Share by Volume LY": "\"FP Share by Volume LY\", [FP Share by Volume LY]", "Net Qty (In-Season)": "\"Net Qty (In-Season)\", [<PERSON> Qty (In-Season)]", "Net Qty (In-Season) LY": "\"Net Qty (In-Season) LY\", [<PERSON> Qty (In-Season) LY]", "Net Sales (In-Season Excl. VAT)": "\"Net Sales (In-Season, Excl. VAT)\", [Net Sales (In-Season, Excl. VAT)]", "Net Sales (In-Season Excl. VAT) LY": "\"Net Sales (In-Season, Excl. VAT) LY\", [Net Sales (In-Season, Excl. VAT) LY]", "Net Sales (In-Season Incl. VAT)": "\"Net Sales (In-Season, Incl. VAT)\", [Net Sales (In-Season, Incl. VAT)]", "Net Sales (In-Season Incl. VAT) LY": "\"Net Sales (In-Season, Incl. VAT) LY\", [Net Sales (In-Season, Incl. VAT) LY]", "In-Season Qty SOB%": "\"In-Season Qty SOB%\", [In-Season Qty SOB%]", "In-Season Qty SOB% LY": "\"In-Season Qty SOB% LY\", [In-Season Qty SOB% LY]", "In-Season Value SOB% (Excl. VAT)": "\"In-Season Value SOB% (Excl. VAT)\", [In-Season Value SOB% (Excl. VAT)]", "In-Season Value SOB% (Excl. VAT) LY": "\"In-Season Value SOB% (Excl. VAT) LY\", [In-Season Value SOB% (Excl. VAT) LY]", "In-Season Value SOB% (Incl. VAT)": "\"In-Season Value SOB% (Incl. VAT)\", [In-Season Value SOB% (Incl. VAT)]", "In-Season Value SOB% (Incl. VAT) LY": "\"In-Season Value SOB% (Incl. VAT) LY\", [In-Season Value SOB% (Incl. VAT) LY]", "ROS by Month": "\"ROS by Month\", [ROS by Month]", "ROS by Quarter": "\"ROS by Quarter\", [ROS by Quarter]", "ROS by Week": "\"ROS by Week\", [ROS by Week]", "ROS by Week LY": "\"ROS by Week LY\", [ROS by Week LY]", "No. of Sales Days": "\"No. of Sales Days\", [No. of Sales Days]", "No. of Sales Days LY": "\"No. of Sales Days LY\", [No. of Sales Days LY]", "Average daily net sales per store": "\"Average daily net sales per store\", [Average daily net sales per store]", "Average daily net sales per store LY": "\"Average daily net sales per store LY\", [Average daily net sales per store LY]", "MPSA (Excl. VAT)": "\"MPSA (Excl. VAT)\", [MPSA (Excl. VAT)]", "MPSA (Excl. VAT) LY": "\"MPSA (Excl. VAT) LY\", [MPSA (Excl. VAT) LY]", "MPSA (Incl. VAT)": "\"MPSA (Incl. VAT)\", [MPSA (Incl. VAT)]", "MPSA (Incl. VAT) LY": "\"MPSA (Incl. VAT) LY\", [MPSA (Incl. VAT) LY]", "Net Selling Space": "\"Net Selling Space\", [Net Selling Space]", "Net Selling Space LY": "\"Net Selling Space LY\", [Net Selling Space LY]", "Average Store Net Selling Space": "\"Average Store Net Selling Space\", [Average Store Net Selling Space]", "Average Store Net Selling Space LY": "\"Average Store Net Selling Space LY\", [Average Store Net Selling Space LY]", "MPSA Efficiency-NetSellingSpace (excl.VAT)": "\"MPSA Efficiency-NetSellingSpace (excl.VAT)\", [MPSA Efficiency-NetSellingSpace (excl.VAT)]", "MPSA Efficiency-NetSellingSpace (excl.VAT) LY": "\"MPSA Efficiency-NetSellingSpace (excl.VAT) LY\", [MPSA Efficiency-NetSellingSpace (excl.VAT) LY]", "MPSA Efficiency-NetSellingSpace (Incl.VAT)": "\"MPSA Efficiency-NetSellingSpace (Incl.VAT)\", [MPSA Efficiency-NetSellingSpace (Incl.VAT)]", "MPSA Efficiency-NetSellingSpace (Incl.VAT) LY": "\"MPSA Efficiency-NetSellingSpace (Incl.VAT) LY\", [MPSA Efficiency-NetSellingSpace (Incl.VAT) LY]", "No.of articles (Sales)": "\"No.of articles (Sales)\", [No.of articles (Sales)]", "No.of Size Count (Sales)": "\"No.of Size Count (Sales)\", [No.of Size Count (Sales)]", "Standard Cost": "\"Standard Cost\", [Standard Cost]", "NetQty (Clean)": "\"NetQty (Clean)\", [NetQty (Clean)]", "NetQty(Clean) LY": "\"NetQty(Clean) LY\", [NetQty(Clean) LY]", "NetSales (Clean Excl.VAT)": "\"NetSales (Clean, Excl.VAT)\", [NetSales (Clean, Excl.VAT)]", "NetSales (Clean Excl.VAT) LY": "\"NetSales (Clean, Excl.VAT) LY\", [NetSales (Clean, Excl.VAT) LY]", "NetSales (Clean Incl.VAT)": "\"NetSales (Clean, Incl.VAT)\", [NetSales (Clean, Incl.VAT)]", "NetSales (Clean Incl.VAT) LY": "\"NetSales (Clean, Incl.VAT) LY\", [NetSales (Clean, Incl.VAT) LY]", "GrossSales (Clean Excl.VAT)": "\"GrossSales (Clean, Excl.VAT)\", [GrossSales (Clean, Excl.VAT)]", "GrossSales (Clean Excl.VAT) LY": "\"GrossSales (Clean, Excl.VAT) LY\", [GrossSales (Clean, Excl.VAT) LY]", "GrossSales (Clean Incl.VAT)": "\"GrossSales (Clean, Incl.VAT)\", [GrossSales (Clean, Incl.VAT)]", "GrossSales (Clean Incl.VAT) LY": "\"GrossSales (Clean, Incl.VAT) LY\", [GrossSales (Clean, Incl.VAT) LY]", "TrafficCount (Clean)": "\"TrafficCount (Clean)\", [TrafficCount (Clean)]", "TrafficCount (Clean) LY": "\"TrafficCount (Clean) LY\", [TrafficCount (Clean) LY]", "Nr.ofSalesTransactions (Clean)": "\"Nr.ofSalesTransactions (Clean)\", [Nr.ofSalesTransactions (Clean)]", "Nr.ofSalesTransactions (Clean) LY": "\"Nr.ofSalesTransactions (Clean) LY\", [Nr.ofSalesTransactions (Clean) LY]", "ATV (Clean Excl.VAT)": "\"ATV (Clean, Excl.VAT)\", [ATV (Clean, Excl.VAT)]", "ATV (Clean Excl.VAT) LY": "\"ATV (Clean, Excl.VAT) LY\", [ATV (Clean, Excl.VAT) LY]", "ATV (Clean Incl.VAT)": "\"ATV (Clean, Incl.VAT)\", [ATV (Clean, Incl.VAT)]", "ATV (Clean Incl.VAT) LY": "\"ATV (Clean, Incl.VAT) LY\", [ATV (Clean, Incl.VAT) LY]", "UPT (Clean)": "\"UPT (Clean)\", [UPT (Clean)]", "UPT (Clean) LY": "\"UPT (Clean) LY\", [UPT (Clean) LY]", "ConversionRate (Clean)": "\"ConversionRate (Clean)\", [ConversionRate (Clean)]", "ConversionRate (Clean) LY": "\"ConversionRate (Clean) LY\", [ConversionRate (Clean) LY]", "Nr.of Stores(Traffic) (Clean)": "\"Nr.of Stores(Traffic) (Clean)\", [Nr.of Stores(Traffic) (Clean)]", "Nr.of Stores(Traffic) (Clean) LY": "\"Nr.of Stores(Traffic) (Clean) LY\", [Nr.of Stores(Traffic) (Clean) LY]", "Average Traffic per Store (Clean)": "\"Average Traffic per Store (Clean)\", [Average Traffic per Store (Clean)]", "Average Traffic per Store (Clean) LY": "\"Average Traffic per Store (Clean) LY\", [Average Traffic per Store (Clean) LY]", "Average Daily Traffic per Store (Clean)": "\"Average Daily Traffic per Store (Clean)\", [Average Daily Traffic per Store (Clean)]", "Average Daily Traffic per Store (Clean) LY": "\"Average Daily Traffic per Store (Clean) LY\", [Average Daily Traffic per Store (Clean) LY]"}