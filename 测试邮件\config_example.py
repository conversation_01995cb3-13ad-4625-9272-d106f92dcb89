"""
邮件配置示例文件
复制此文件为 config.py 并填入你的实际配置信息
"""

# SMTP 配置 (用于个人 Outlook 账户)
SMTP_CONFIG = {
    "sender_email": "<EMAIL>",
    "sender_password": "your_password_or_app_password",  # 建议使用应用密码
}

# Microsoft Graph API 配置 (用于企业 Office 365 账户)
GRAPH_CONFIG = {
    "tenant_id": "your-tenant-id",
    "client_id": "your-client-id", 
    "client_secret": "your-client-secret",
    "sender_email": "<EMAIL>"
}

# 邮件模板
EMAIL_TEMPLATES = {
    "welcome": {
        "subject": "欢迎使用我们的服务",
        "body": """
        <html>
            <body>
                <h2>欢迎！</h2>
                <p>感谢您注册我们的服务。</p>
                <p>如有任何问题，请随时联系我们。</p>
            </body>
        </html>
        """,
        "is_html": True
    },
    "notification": {
        "subject": "系统通知",
        "body": "这是一条系统通知消息。",
        "is_html": False
    }
}
