#!/usr/bin/env python3
"""
PowerBI 数据下载器类
将 pbi_download_new.py 的函数式代码封装为类，保持原有逻辑不变
"""

from datetime import datetime, timedelta
import pandas as pd
import os
import json
import platform
import msal
from concurrent.futures import ThreadPoolExecutor, as_completed
import hashlib
import win32com.client 
import pythoncom
import traceback
import gc
import csv
from typing import Optional, Tuple, List, Dict
import time
import uuid


class PowerBIDownloader:
    """PowerBI 数据下载器类 - 保持原有逻辑不变"""
    
    def __init__(self, client_id=None, client_secret=None, tenant_id=None, workspace_name=None, base_dir=None):
        """初始化 PowerBI 下载器"""
        # PowerBI REST API配置
        self.client_id = client_id or "ea0616ba-638b-4df5-95b9-636659ae5121"
        self.client_secret = client_secret or "****************************************"
        self.tenant_id = tenant_id or "3bfeb222-e42c-4535-aace-ea6f7751369b"
        self.workspace_name = workspace_name or "DNA GCA_DEV:TW MAJOR REPORT"
        
        # 配置文件的地址
        self.base_dir = base_dir or "C:/Users/<USER>/Desktop/test/测试下载"
        
        # 时间维度映射
        self.TIME_DIMENSION_MAP = {
            "Snapshot Date": "date",
            "DATE": "date",
            "MONTH": "month",
            "QUARTER": "quarter",
            "YEAR": "year"
        }
        
        # 临时目录配置
        self.TEMP_DIR = "temp_batches_practical"
        self.custom_dir = self.get_default_download_dir()
        self.temp_dir = os.path.join(self.custom_dir, self.TEMP_DIR)
        
        # 确保目录存在
        os.makedirs(self.custom_dir, exist_ok=True)
        os.makedirs(self.temp_dir, exist_ok=True)
        
        # 加载配置
        self.PRACTICAL_CONFIG_LIST = self.load_json_mapping('practical_config.json')
        
        print(f"✅ PowerBI 下载器初始化完成")
        print(f"📁 下载目录: {self.custom_dir}")
        print(f"🏢 工作区: {self.workspace_name}")
    
    def get_default_download_dir(self):
        """获取默认下载目录"""
        system = platform.system()
        if system == "Windows":
            return os.path.join(os.path.expanduser("~"), "Desktop", "测试", "实用优化下载")
        elif system == "Darwin":  # macOS
            return os.path.join(os.path.expanduser("~"), "Desktop", "PowerBI实用优化下载")
        else:  # Linux
            return os.path.join(os.path.expanduser("~"), "Downloads", "PowerBI实用优化下载")
    
    def get_access_token(self):
        """获取access_token 弹框登录模式"""
        try:
            authority = "https://login.microsoftonline.com/common"
            app = msal.PublicClientApplication(client_id=self.client_id, authority=authority)
            scopes = ["https://analysis.windows.net/powerbi/api/.default"]
            
            print("🔄 正在获取token...")
            result = app.acquire_token_interactive(scopes=scopes)
            
            if "access_token" in result:
                token = result["access_token"]
                print(f"📅 Token过期时间: {result.get('expires_in', 'Unknown')}秒")
                return token
            else:
                error_msg = result.get("error_description", "Unknown error")
                print(f"❌ 获取token失败: {error_msg}")
                raise Exception(f"Failed to get token: {error_msg}")
        except Exception as e:
            print(f"❌ 获取access_token异常: {e}")
            raise
    
    def load_json_mapping(self, filename):
        """加载映射 - 使用正确的路径"""
        file_path = os.path.join(self.base_dir, filename)
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    
    def get_mapping_files(self, name):
        """根据报表名称获取映射文件名"""
        if name == "TW Inventory":
            dim_file = 'dim_mapping_inventory.json'
            fcst_file = 'fcst_mapping_inventory.json'
            filter_file = 'filter_mapping_inventory.json'
        elif name == "TW Transaction Template":
            dim_file = 'dim_mapping_transaction.json'
            fcst_file = 'fcst_mapping_transaction.json'
            filter_file = 'filter_mapping_transaction.json'
        else:
            raise ValueError(f"未知的name参数: {name}")
        return dim_file, fcst_file, filter_file
    
    def load_all_mappings(self, name):
        """加载所有映射文件"""
        dim_file, fcst_file, filter_file = self.get_mapping_files(name)
        dim_mapping = self.load_json_mapping(dim_file)
        fcst_mapping = self.load_json_mapping(fcst_file)
        filter_mapping = self.load_json_mapping(filter_file)
        return dim_mapping, fcst_mapping, filter_mapping
    
    def build_dim_actual_names(self, dim_mapping):
        """构建维度实际名称映射"""
        dim_actual_names = {}
        for dim, expr in dim_mapping.items():
            table_part, column_part = expr.split('[', 1)
            table_name = table_part.strip("'")
            column_name = column_part.rstrip(']')
            actual_name = f"{table_name}[{column_name}]"
            dim_actual_names[dim] = actual_name
        return dim_actual_names
    
    def build_fcst_actual_names(self, fcst_mapping):
        """构建指标实际名称映射"""
        fcst_actual_names = {}
        for fcst, expr in fcst_mapping.items():
            if '[' in expr and ']' in expr:
                start = expr.find('[') + 1
                end = expr.find(']', start)
                if end != -1:
                    metric_name = expr[start:end]
                    actual_name = f"[{metric_name}]"
                else:
                    actual_name = expr.split(',')[0].strip('"')
            else:
                actual_name = expr.split(',')[0].strip('"')
            fcst_actual_names[fcst] = actual_name
        return fcst_actual_names
    
    def get_quarter_start_end(self, year, quarter):
        """根据自然天计算季度的开始和结束日期"""
        # 计算季度开始月份
        start_month = (quarter - 1) * 3 + 1
        
        # 计算季度结束月份
        end_month = start_month + 2
        
        # 开始日期是季度第一个月的第一天
        start_date = datetime(year, start_month, 1)
        
        # 结束日期是季度最后一个月的最后一天
        if end_month == 12:
            end_date = datetime(year+1, 1, 1) - timedelta(days=1)
        else:
            end_date = datetime(year, end_month+1, 1) - timedelta(days=1)
        
        return start_date, end_date
    
    def get_quarter(self, dt):
        """获取日期所在的季度"""
        return (dt.month - 1) // 3 + 1
    
    def get_practical_config(self, batch_type, total_units):
        """根据时间维度和时间跨度获取实用优化配置"""
        for config in self.PRACTICAL_CONFIG_LIST:
            if config[0] == batch_type and config[1] <= total_units <= config[2]:
                return config[3], config[4]
        
        # 如果没有匹配项，返回默认配置
        default_config = {
            'date': (5, 10),
            'month': (5, 10),
            'quarter': (5, 10),
            'year': (1, 1)
        }
        return default_config.get(batch_type, (10, 10))

    def calculate_and_configure_batch(self, batch_type, start_date, end_date):
        """计算时间跨度并配置优化参数"""
        if batch_type == 'date':
            total_units = (end_date - start_date).days + 1
            unit_name = "天"

        elif batch_type == 'month':
            total_units = (end_date.year - start_date.year) * 12 + (end_date.month - start_date.month) + 1
            unit_name = "个月"

        elif batch_type == 'quarter':
            start_quarter = self.get_quarter(start_date)
            end_quarter = self.get_quarter(end_date)
            total_units = (end_date.year - start_date.year) * 4 + (end_quarter - start_quarter) + 1
            unit_name = "个季度"

        elif batch_type == 'year':
            total_units = end_date.year - start_date.year + 1
            unit_name = "年"
        else:
            # 默认按天处理
            total_units = (end_date - start_date).days + 1
            unit_name = "天"
            batch_type = 'date'

        print(f"📅📅 时间跨度: {total_units} {unit_name}")

        # 获取优化配置
        PRACTICAL_BATCH, PRACTICAL_CONCURRENT = self.get_practical_config(batch_type, total_units)

        print(f"⚡⚡ 实用优化配置: {PRACTICAL_BATCH}{unit_name.replace('个', '')}批次 | {PRACTICAL_CONCURRENT}并发")

        return total_units, unit_name, PRACTICAL_BATCH, PRACTICAL_CONCURRENT

    def build_dax_filter(self, filter_mapping, **kwargs):
        """构建DAX过滤条件"""
        filter_clauses = []
        for key, value in kwargs.items():
            if value and key in filter_mapping:
                dax_field = filter_mapping[key]
                if isinstance(value, list):
                    value_set = "{" + ",".join([f'\"{v}\"' for v in value]) + "}"
                    filter_clauses.append(f"{dax_field} in {value_set}")
                else:
                    filter_clauses.append(f"{dax_field} = \"{value}\"")
        if filter_clauses:
            return ",\n                " + ",\n                ".join(filter_clauses)
        return ""

    def parse_str_to_list(self, val):
        """将字符串转为列表"""
        if val is None:
            return None
        if isinstance(val, list):
            # 如果是 ["All"]，直接返回 None
            if len(val) == 1 and val[0] == "All":
                return None
            return val
        if isinstance(val, str):
            try:
                result = json.loads(val)
                if isinstance(result, list):
                    if len(result) == 1 and result[0] == "All":
                        return None
                    return result
                else:
                    if result == "All":
                        return None
                    return [result]
            except Exception:
                if val == "All":
                    return None
                return [val]
        return [val]

    def get_file_stats(self, file_path):
        """同时获取行数和列数"""
        row_count, col_count = 0, 0
        with open(file_path, 'r', encoding='utf-8-sig') as f:
            reader = csv.reader(f)
            for i, row in enumerate(reader):
                if i == 0:  # 首行获取列数
                    col_count = len(row)
                row_count = i  # 末行索引=实际行数
        return row_count, col_count

    def cleanup_temp_files(self, request_uuid):
        """清理本次请求的临时文件"""
        try:
            import glob
            pattern = os.path.join(self.temp_dir, f"batch_{request_uuid}_*.csv")
            temp_files = glob.glob(pattern)
            removed_count = 0

            for temp_file in temp_files:
                try:
                    os.remove(temp_file)
                    removed_count += 1
                except:
                    pass

            print(f"🧹 清理临时文件: {removed_count} 个文件已删除")

        except Exception as e:
            print(f"⚠️  清理临时文件时出现异常: {e}")

    def build_practical_dax(self, dim_list, fcst_list, start_date, end_date, dim_mapping, fcst_mapping, pbi_name, dax_filter=""):
        """拼接DAX查询"""
        if not dim_list or not fcst_list:
            raise ValueError("维度和指标参数不能为空")

        # 构建维度字段列表
        dim_fields = []
        for dim in dim_list:
            if dim not in dim_mapping:
                raise ValueError(f"无效的维度字段: {dim}")
            dim_fields.append(dim_mapping[dim])

        # 构建指标字段列表
        fcst_fields = []
        for fcst in fcst_list:
            if fcst not in fcst_mapping:
                raise ValueError(f"无效的指标字段: {fcst}")
            fcst_fields.append(fcst_mapping[fcst])

        try:
            # 解析日期
            start_parts = start_date.split(',')
            end_parts = end_date.split(',')

            if len(start_parts) != 3 or len(end_parts) != 3:
                raise ValueError("日期格式错误，应为 YYYY,MM,DD")

            if pbi_name == "TW Inventory":
                date_table = "'Dim_date'[snapshot_date]"
            elif pbi_name == "TW Transaction Template":
                date_table="'vw_dim_calendar'[calendar_day]"
            else:
                raise ValueError(f"未知的报表名称: {pbi_name}")

            dax_query = f"""EVALUATE
                CALCULATETABLE(
                    SUMMARIZECOLUMNS(
                        {", ".join(dim_fields)},
                        {", ".join(fcst_fields)}
                    ),
                    DATESBETWEEN(
                        {date_table},
                        DATE({start_parts[0]}, {start_parts[1]}, {start_parts[2]}),
                        DATE({end_parts[0]}, {end_parts[1]}, {end_parts[2]})
                    ){dax_filter}
                )"""

            return dax_query
        except Exception as e:
            print(f"❌ DAX查询构建失败: {e}")
            raise

    def fetch_data(self, dim_list, fcst_list, dax_query, batch_info, access_token, dim_actual_names, fcst_actual_names, pbi_name):
        """查询数据 - 保持原有逻辑"""
        print(f"dax_query: {dax_query}")
        conn = None
        rs = None
        try:
            # 线程中初始化COM
            pythoncom.CoInitialize()

            # 创建连接对象
            conn = win32com.client.Dispatch("ADODB.Connection")

            # 构建连接字符串
            connection_string = (
                f"Provider=MSOLAP;"
                f"Data Source=powerbi://api.powerbi.com/v1.0/myorg/{self.workspace_name};"
                f"Initial Catalog={pbi_name};"
                f"MDX Compatibility=1;"
                f"Safety Options=2;"
                f"ConnectTo=11;"
                f"MDPROP_MSMD_Safety_Options=2;"
                f"Locale Identifier=1033;"
                f"EffectiveUserName=PowerBIUser;"
                f"Pwd={access_token};"
            )

            # 打开连接
            conn.Open(connection_string)

            # 执行查询
            rs = conn.Execute(dax_query)[0]

            # 获取字段信息
            fields = rs.Fields
            field_count = fields.Count

            # 构建列名映射
            column_names = []
            for i in range(field_count):
                field_name = fields.Item(i).Name

                # 查找对应的中文名称
                chinese_name = field_name
                for dim, actual_name in dim_actual_names.items():
                    if actual_name == field_name:
                        chinese_name = dim
                        break

                for fcst, actual_name in fcst_actual_names.items():
                    if actual_name == field_name:
                        chinese_name = fcst
                        break

                column_names.append(chinese_name)

            # 读取数据
            data_rows = []
            row_count = 0

            while not rs.EOF:
                row = []
                for i in range(field_count):
                    value = rs.Fields.Item(i).Value
                    if value is None:
                        row.append("")
                    else:
                        row.append(str(value))
                data_rows.append(row)
                row_count += 1
                rs.MoveNext()

            # 生成CSV文件
            batch_id = batch_info['batch_id']
            batch_file = os.path.join(self.temp_dir, f"batch_{batch_id}.csv")

            with open(batch_file, 'w', newline='', encoding='utf-8-sig') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(column_names)  # 写入表头
                writer.writerows(data_rows)    # 写入数据

            print(f"✅ 批次 {batch_info['batch_num']} 完成: {row_count} 行数据")
            return batch_file, row_count

        except Exception as e:
            error_msg = str(e)
            print(f"❌ 批次 {batch_info['batch_num']} 失败: {error_msg}")

            # 提供更详细的错误诊断
            if "多步 OLE DB 操作产生错误" in error_msg:
                print(f"   🔍 OLE DB 连接错误，可能原因：")
                print(f"      - PowerBI 服务暂时不可用")
                print(f"      - 网络连接问题")
                print(f"      - Token 权限不足")
                print(f"      - 工作区 '{self.workspace_name}' 不存在或无权限")
            elif "对象关闭时，不允许操作" in error_msg:
                print(f"   🔍 连接已关闭，这是正常的清理过程")
            else:
                print(f"   🔍 其他错误，请检查配置和权限")

            return None, 0
        finally:
            # 清理资源
            try:
                if rs:
                    rs.Close()
                if conn:
                    conn.Close()
            except Exception as e:
                print(f"关闭资源时出错: {str(e)}")

            pythoncom.CoUninitialize()

    def merge_batch_files(self, batch_files, pbi_name):
        """流式合并批次文件"""
        try:
            if not batch_files:
                return None

            # 创建最终合并文件
            timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
            final_file = os.path.join(self.custom_dir, f"{pbi_name}_{timestamp}.csv")

            with open(final_file, 'w', newline='', encoding='utf-8-sig') as outfile:
                writer = csv.writer(outfile)
                header_written = False

                for batch_file in batch_files:
                    if os.path.exists(batch_file):
                        with open(batch_file, 'r', encoding='utf-8-sig') as infile:
                            reader = csv.reader(infile)
                            for i, row in enumerate(reader):
                                if i == 0:  # 表头行
                                    if not header_written:
                                        writer.writerow(row)
                                        header_written = True
                                else:  # 数据行
                                    writer.writerow(row)

            print(f"📁 合并完成: {final_file}")
            return final_file

        except Exception as e:
            print(f"❌❌ 流式合并失败: {e}")
            return None

    def generate_inventory_date_batches(self, Snapshot_date):
        """为库存报表生成批次（每个日期作为一个批次）"""
        try:
            # 生成一个请求ID
            request_uuid = uuid.uuid4().hex[:8]
            print(f"请求总批次:{request_uuid}")

            if not Snapshot_date:
                raise ValueError("Snapshot_date参数不能为空")

            # 转换日期格式并去重
            unique_dates = set()
            for date_str in Snapshot_date:
                date_str_comma = date_str.replace("/", ",")
                unique_dates.add(date_str_comma)

            # 生成批次（每个日期作为一个批次）
            batches = []
            for i, date_str_comma in enumerate(sorted(unique_dates)):
                year, month, day = date_str_comma.split(",")
                display_date = f"{year}-{month.zfill(2)}-{day.zfill(2)}"

                batch_id = hashlib.md5(f"INVENTORY_{date_str_comma}".encode()).hexdigest()[:16]
                full_batch_id = f"{request_uuid}_{batch_id}"

                batches.append({
                    'batch_id': full_batch_id,
                    'batch_num': i + 1,
                    'start_date': date_str_comma,
                    'end_date': date_str_comma,
                    'date_display': display_date,
                    'days_count': 1
                })

            # 计算并发数（等于日期数量）
            PRACTICAL_CONCURRENT = len(batches)

            print(f"📅 生成库存分批计划 (快照日期):")
            print(f"   快照日期数量: {len(batches)}个")
            print(f"   优化策略: 每个日期作为一个批次 | 并发数: {PRACTICAL_CONCURRENT}")

            return batches, PRACTICAL_CONCURRENT, request_uuid

        except Exception as e:
            print(f"❌❌❌❌ 生成库存批次失败: {e}")
            import traceback
            traceback.print_exc()
            return [], 1, ""

    def fetch_practical_data(self,access_token,dim_params, fcst_params, date_min, date_max, batch_type, PRACTICAL_BATCH, PRACTICAL_CONCURRENT,
                               dim_mapping, fcst_mapping, dim_actual_names, fcst_actual_names, pbi_name,
                               dax_filter="", custom_batches=None, request_uuid=None):
        try:
            dim_list = [d.strip() for d in dim_params.split(',')]
            fcst_list = [f.strip() for f in fcst_params.split(',')]
   
            # 如果有自定义批次，使用自定义批次
            if custom_batches is not None:
                batches = custom_batches
                if request_uuid is None:
                    request_uuid = uuid.uuid4().hex[:8]
                print(f"⚡ 使用自定义批次: {len(batches)}个")
            else:
                # 否则按常规方式生成批次
                batches, request_uuid = self.generate_practical_date_batches(date_min, date_max, batch_type, PRACTICAL_BATCH)
                
            if not batches:
                raise ValueError("generate_practical_date_batches 返回了空列表")
            print(f"\n🚀 开始执行 {len(batches)} 个批次下载")
            print(f"⚡ 实用优化配置: {PRACTICAL_CONCURRENT}并发 | {PRACTICAL_BATCH}天/批次 ")
            successful_batches = 0
            failed_batches = 0
            start_time = time.time()
            with ThreadPoolExecutor(max_workers=PRACTICAL_CONCURRENT) as executor:
                future_to_batch = {}
                for batch in batches:
                    print(f"批次batch_num: {batch['batch_num']} ,批次batch_id:  {batch['batch_id']},批次start_date:  {batch['start_date']} ,批次end_date:  {batch['end_date']}")
                    dax_query = self.build_practical_dax(dim_list, fcst_list, batch['start_date'], batch['end_date'], dim_mapping, fcst_mapping, pbi_name, dax_filter)
                    dim_list = [d.strip() for d in dim_params.split(',')]
                    fcst_list = [f.strip() for f in fcst_params.split(',')]
                    future = executor.submit(self.fetch_data, dim_list, fcst_list, dax_query, batch, access_token, dim_actual_names, fcst_actual_names, pbi_name)
                    future_to_batch[future] = batch
                batch_files = []
                for future in as_completed(future_to_batch):
                    batch = future_to_batch[future]
                    try:
                        file_path = future.result()
                        if file_path is not None:
                            batch_files.append(f"batch_{batch['batch_id']}.csv")
                            successful_batches += 1
                            progress_pct = (successful_batches / len(batches)) * 100
                            print(f"   📈 进度: {progress_pct:.1f}% | 成功: {successful_batches}/{len(batches)}")
                        else:
                            failed_batches += 1
                            print(f"   ❌ 批次失败，继续...")
                    except Exception as batch_error:
                        failed_batches += 1
                        print(f"❌ 批次 {batch['batch_num']} 处理异常: {batch_error}")
                        continue
                      
            if successful_batches == 0:
                raise Exception("所有批次都失败了，无数据可合并")
              
            start_connect = time.time()
            print(f"\n🔄 合并 {successful_batches} 个批次的数据...")
            output_file = self.merge_batch_files(batch_files, pbi_name)
            time_connect = time.time() - start_connect
            print(f"\n🔄 合并 完成耗时: {time_connect:.2f}秒")
            file_size = os.path.getsize(output_file)
            file_size_mb = round(file_size / (1024 * 1024), 2)
            rows, cols = self.get_file_stats(output_file)
            self.cleanup_temp_files(request_uuid)
            end_time = time.time()
            total_time = end_time - start_time
            hours = int(total_time // 3600)
            minutes = int((total_time % 3600) // 60)
            seconds = int(total_time % 60)
            time_str = f"{hours}小时{minutes}分{seconds}秒" if hours > 0 else f"{minutes}分{seconds}秒"
            print(f"\n✅ 实用优化下载完成!")
            print(f"📁 文件路径: {output_file}")
            print(f"📊 最终数据: {rows:,} 行, {cols} 列")
            print(f"📈 文件大小: {file_size_mb} MB")
            print(f"📦 成功批次: {successful_batches}/{len(batches)}")
            print(f"❌ 失败批次: {failed_batches}")
            print(f"⚡ 用时: {time_str}")
            return output_file, rows, cols, file_size_mb, time_str
        except Exception as e:
            print(f"❌ 实用优化下载失败: {e}")
            raise



    def download_data(self, json_data):
        """
        主要的数据下载方法 - 保持原有逻辑不变

        Args:
            json_data: JSON格式的参数数据

        Returns:
            tuple: (文件路径, 行数, 列数, 文件大小MB, 耗时)
        """
        try:
            # 获取访问令牌
            access_token = self.get_access_token()

            # 如果传入的是字符串，先解析为字典
            if isinstance(json_data, str):
                import json
                data = json.loads(json_data)
            else:
                data = json_data

            timestamp1 = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            print(f"\n{'='*20} PowerBI 实用优化模式 {'='*20}")
            print(f"开始时间: {timestamp1}")
            print(f"📋 接收到的参数: {data}")

            # 提取必需参数
            report_name = data.get("report_name")
            dim_params = data.get("dim")
            fcst_params = data.get("fcst")

            if not report_name or not dim_params or not fcst_params:
                raise ValueError("缺少必要参数: report_name, dim, fcst")

            print(f"📊 报表名称: {report_name}")
            print(f"📏 维度参数: {dim_params}")
            print(f"📈 指标参数: {fcst_params}")

            # 动态加载映射和实际名称
            dim_mapping, fcst_mapping, filter_mapping = self.load_all_mappings(report_name)
            dim_actual_names = self.build_dim_actual_names(dim_mapping)
            fcst_actual_names = self.build_fcst_actual_names(fcst_mapping)

            # 动态解析所有可能的筛选参数
            filter_param_names = [
                'launch', 'pro_type', 'pro_div', 'pro_article', 'pro_category', 'pro_yeezy',
                'sto_code', 'sto_status', 'sto_main_channel', 'sto_format', 'sto_type',
                'article_no', 'stock_category', 'global_store_code', 'store_type',
                'stock_position', 'inv_owner'
            ]

            filter_param_values = {}
            for param in filter_param_names:
                if param in data:
                    filter_param_values[param] = self.parse_str_to_list(data[param])
                    if filter_param_values[param] is not None:
                        print(f"🔍 过滤条件 {param}: {filter_param_values[param]}")

            # 生成 DAX filter 字符串
            dax_filter = self.build_dax_filter(filter_mapping, **filter_param_values)
            if dax_filter:
                print(f"🔍 DAX过滤器: {dax_filter}")

            # 根据报表类型处理
            if report_name == "TW Inventory":
                # 库存报表处理
                snapshot_dates = data.get("Snapshot_date")
                if not snapshot_dates:
                    raise ValueError("库存报表需要提供Snapshot_date参数")

                # 确保是列表格式
                if isinstance(snapshot_dates, str):
                    try:
                        import json
                        snapshot_dates = json.loads(snapshot_dates)
                    except:
                        snapshot_dates = [snapshot_dates]

                print(f"🔄 库存报表处理模式 | 快照日期: {len(snapshot_dates)}个")
                print(f"📅 快照日期: {snapshot_dates}")

                # 生成批次（每个日期作为一个批次）
                batches, PRACTICAL_CONCURRENT, request_uuid = self.generate_inventory_date_batches(snapshot_dates)
                PRACTICAL_BATCH = 1
                batch_type = 'date'

                # 使用自定义的批次列表
                file_path, row_count, col_count, file_size_mb, time_str = self.fetch_practical_data(
                    access_token, dim_params, fcst_params, "", "", batch_type, PRACTICAL_BATCH, PRACTICAL_CONCURRENT,
                    dim_mapping, fcst_mapping, dim_actual_names, fcst_actual_names, report_name,
                    dax_filter=dax_filter, custom_batches=batches, request_uuid=request_uuid
                )
            else:
                # 交易报表处理
                date_min = data.get("damin")
                date_max = data.get("damax")

                if not date_min or not date_max:
                    raise ValueError("交易报表需要提供damin和damax参数")

                # 转换日期格式
                date_min = date_min.replace("/", ",")
                date_max = date_max.replace("/", ",")

                print(f"📅 开始日期: {date_min}")
                print(f"📅 结束日期: {date_max}")

                # 计算预期数据量
                start_parts = date_min.split(',')
                end_parts = date_max.split(',')
                start_date = datetime(int(start_parts[0]), int(start_parts[1]), int(start_parts[2]))
                end_date = datetime(int(end_parts[0]), int(end_parts[1]), int(end_parts[2]))

                # 按优先级检测时间维度字段
                batch_type = next(
                    (self.TIME_DIMENSION_MAP[field] for field in ["Snapshot Date","DATE", "MONTH", "QUARTER", "YEAR"]
                     if field in dim_params),
                    'date'  # 默认值
                )

                print(f"🔍 分批策略: {batch_type}")

                # 计算时间跨度和配置
                total_units, unit_name, PRACTICAL_BATCH, PRACTICAL_CONCURRENT = self.calculate_and_configure_batch(
                    batch_type, start_date, end_date
                )

                # 获取数据并生成CSV文件
                file_path, row_count, col_count, file_size_mb, time_str = self.fetch_practical_data(
                    access_token, dim_params, fcst_params, date_min, date_max, batch_type, PRACTICAL_BATCH, PRACTICAL_CONCURRENT,
                    dim_mapping, fcst_mapping, dim_actual_names, fcst_actual_names, report_name,
                    dax_filter=dax_filter
                )

            # 计算运行时间
            timestamp2 = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            print(f"\n{'='*50}")
            print(f"🎉 下载完成: {timestamp2}")
            print(f"⏱️ 总运行时长: {time_str}")
            print(f"📊 最终数据: {row_count:,}行, {col_count}列")
            print(f"💾 CSV文件大小: {file_size_mb} MB")
            print(f"📁 文件路径: {file_path}")
            print(f"💻 操作系统: {platform.system()}")
            print(f"{'='*50}")

            return file_path, access_token

        except Exception as e:
            error_msg = f"实用优化下载失败: {str(e)}"
            print(f"❌❌❌❌ {error_msg}")
            raise
