from send_email import send_custom_email

def test_send_email(url):
    to_email = "<EMAIL>"
    subject = "报告下载成功"
    
    # 使用 HTML 格式的内容（包含超链接）
    message = f"""
    <html>
    <body>
        <p>您好，</p>
        <p>您的报告已经成功下载并处理完成。</p>
        <p>详细信息：</p>
        <ul>
            <li><strong>状态：</strong>成功</li>
            <li><strong>报告地址：</strong><a href='{url}'>点击查看报告</a></li>
        </ul>
        <p>如有任何问题，请及时联系。</p>
        <p>祝好！</p>
    </body>
    </html>
    """.strip()

    # 发送 HTML 格式的邮件
    success = send_custom_email(
        to_email, 
        subject, 
        message,
        True
    )

    if success:
        print("✅ 邮件发送成功！")
    else:
        print("❌ 邮件发送失败！")
    return success

def main():
    url = "https://www.baidu.com/"
    test_send_email(url)

if __name__ == "__main__":
    main()