#!/usr/bin/env python3
"""
API适配器 - 将FastAPI参数转换为PowerBI下载器需要的JSON格式
"""

import json
from typing import Optional
from pbi_downloader_class import PowerBIDownloader


class PowerBIAPIAdapter:
    """PowerBI API适配器类"""
    
    def __init__(self):
        self.downloader = PowerBIDownloader()
    
    def convert_api_params_to_json(self, 
                                  reprot_name: str,
                                  user_name: str,
                                  dim: str,
                                  fcst: str,
                                  damin: Optional[str] = None,
                                  damax: Optional[str] = None,
                                  Snapshot_date: Optional[str] = None,
                                  launch: Optional[str] = None,
                                  pro_type: Optional[str] = None,
                                  pro_div: Optional[str] = None,
                                  pro_article: Optional[str] = None,
                                  pro_category: Optional[str] = None,
                                  pro_yeezy: Optional[str] = None,
                                  sto_code: Optional[str] = None,
                                  sto_status: Optional[str] = None,
                                  sto_main_channel: Optional[str] = None,
                                  sto_format: Optional[str] = None,
                                  sto_type: Optional[str] = None,
                                  article_no: Optional[str] = None,
                                  stock_category: Optional[str] = None,
                                  global_store_code: Optional[str] = None,
                                  store_type: Optional[str] = None,
                                  stock_position: Optional[str] = None,
                                  inv_owner: Optional[str] = None):
        """
        将FastAPI的Query参数转换为JSON格式
        
        Args:
            reprot_name: 报表名称 (注意保持原API的拼写)
            user_name: 用户邮件
            dim: 维度参数，用逗号分隔
            fcst: 指标参数，用逗号分隔
            damin: 开始日期，格式:YYYY/MM/DD
            damax: 结束日期，格式:YYYY/MM/DD
            Snapshot_date: 库存日期
            其他参数: 各种过滤条件
            
        Returns:
            dict: 转换后的JSON格式数据
        """
        
        # 基础参数
        json_data = {
            "report_name": reprot_name,  # 修正字段名
            "user_name": user_name,
            "dim": dim,
            "fcst": fcst
        }
        
        # 日期参数
        if damin:
            json_data["damin"] = damin
        if damax:
            json_data["damax"] = damax
        if Snapshot_date:
            json_data["Snapshot_date"] = self._parse_param_value(Snapshot_date)
        
        # 过滤参数列表
        filter_params = {
            "launch": launch,
            "pro_type": pro_type,
            "pro_div": pro_div,
            "pro_article": pro_article,
            "pro_category": pro_category,
            "pro_yeezy": pro_yeezy,
            "sto_code": sto_code,
            "sto_status": sto_status,
            "sto_main_channel": sto_main_channel,
            "sto_format": sto_format,
            "sto_type": sto_type,
            "article_no": article_no,
            "stock_category": stock_category,
            "global_store_code": global_store_code,
            "store_type": store_type,
            "stock_position": stock_position,
            "inv_owner": inv_owner
        }
        
        # 添加非空的过滤参数
        for key, value in filter_params.items():
            if value is not None:
                json_data[key] = self._parse_param_value(value)
        
        return json_data
    
    def _parse_param_value(self, value):
        """解析参数值，支持字符串和JSON格式"""
        if value is None:
            return None
        
        if isinstance(value, str):
            # 尝试解析JSON字符串
            try:
                parsed = json.loads(value)
                return parsed
            except (json.JSONDecodeError, ValueError):
                # 如果不是JSON格式，直接返回字符串
                return value
        
        return value
    
    async def get_practical_data(self,
                                reprot_name: str,
                                user_name: str,
                                dim: str,
                                fcst: str,
                                damin: Optional[str] = None,
                                damax: Optional[str] = None,
                                Snapshot_date: Optional[str] = None,
                                launch: Optional[str] = None,
                                pro_type: Optional[str] = None,
                                pro_div: Optional[str] = None,
                                pro_article: Optional[str] = None,
                                pro_category: Optional[str] = None,
                                pro_yeezy: Optional[str] = None,
                                sto_code: Optional[str] = None,
                                sto_status: Optional[str] = None,
                                sto_main_channel: Optional[str] = None,
                                sto_format: Optional[str] = None,
                                sto_type: Optional[str] = None,
                                article_no: Optional[str] = None,
                                stock_category: Optional[str] = None,
                                global_store_code: Optional[str] = None,
                                store_type: Optional[str] = None,
                                stock_position: Optional[str] = None,
                                inv_owner: Optional[str] = None):
        """
        适配原有的get_practical_data API接口
        
        这个方法保持与原FastAPI接口相同的参数签名，
        但内部使用新的PowerBIDownloader类来处理请求
        """
        try:
            # 转换参数为JSON格式
            json_data = self.convert_api_params_to_json(
                reprot_name=reprot_name,
                user_name=user_name,
                dim=dim,
                fcst=fcst,
                damin=damin,
                damax=damax,
                Snapshot_date=Snapshot_date,
                launch=launch,
                pro_type=pro_type,
                pro_div=pro_div,
                pro_article=pro_article,
                pro_category=pro_category,
                pro_yeezy=pro_yeezy,
                sto_code=sto_code,
                sto_status=sto_status,
                sto_main_channel=sto_main_channel,
                sto_format=sto_format,
                sto_type=sto_type,
                article_no=article_no,
                stock_category=stock_category,
                global_store_code=global_store_code,
                store_type=store_type,
                stock_position=stock_position,
                inv_owner=inv_owner
            )
            
            print(f"🔄 API参数转换完成")
            print(f"📋 JSON数据: {json_data}")
            
            # 调用下载器
            result = self.downloader.download_data(json_data)
            
            return result
            
        except Exception as e:
            print(f"❌ API适配器处理失败: {e}")
            raise


# 使用示例
def example_api_usage():
    """API适配器使用示例"""
    print("🌐 API适配器使用示例")
    print("=" * 40)
    
    # 创建适配器
    adapter = PowerBIAPIAdapter()
    
    # 模拟FastAPI接收到的参数
    api_params = {
        "reprot_name": "TW Inventory",
        "user_name": "<EMAIL>",
        "dim": "Snapshot Date,Article No,Global Store Code",
        "fcst": "Stock Qty,Stock Value",
        "Snapshot_date": '["2024/01/01", "2024/01/02"]',
        "article_no": '["All"]',
        "stock_category": '["FOOTWEAR"]'
    }
    
    print("📥 接收到的API参数:")
    for key, value in api_params.items():
        print(f"   {key}: {value}")
    
    try:
        # 转换参数
        json_data = adapter.convert_api_params_to_json(**api_params)
        
        print("\n📤 转换后的JSON数据:")
        print(json.dumps(json_data, indent=2, ensure_ascii=False))
        
        # 调用下载方法
        print("\n🚀 开始下载...")
        result = adapter.downloader.download_data(json_data)
        
        print(f"✅ 下载完成: {result}")
        
    except Exception as e:
        print(f"❌ 处理失败: {e}")


if __name__ == "__main__":
    example_api_usage()
