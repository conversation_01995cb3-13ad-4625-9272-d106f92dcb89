{"Physical Stock Qty": "\"Physical Stock Qty\", 'Inv Metrics'[Physical Stock Qty]", "Unrestricted Stock Qty": "\"Unrestricted Stock Qty\", 'Inv Metrics'[Unrestricted Stock Qty]", "QI Stock Qty": "\"QI Stock Qty\", 'Inv Metrics'[QI Stock Qty]", "Blocked Stock Qty": "\"Blocked Stock Qty\", 'Inv Metrics'[Blocked Stock Qty]", "Assginend Stock": "\"Assginend Stock\", 'Inv Metrics'[Assginend Stock]", "Store Stock In Transit Qty (DC To Store)": "\"Store Stock In Transit Qty (DC To Store)\", 'Inv Metrics'[Store Stock In Transit Qty (DC To Store)]", "Store Stock In Transit Qty (Store To Store)": "\"Store Stock In Transit Qty (Store To Store)\", 'Inv Metrics'[Store Stock In Transit Qty (Store To Store)]", "Store Stock In Transit Qty": "\"Store Stock In Transit Qty\", 'Inv Metrics'[Store Stock In Transit Qty]", "Store Stock On Hand Qty": "\"Store Stock On Hand Qty\", 'Inv Metrics'[Store Stock On Hand Qty]", "Total Store Stock Qty": "\"Total Store Stock Qty\", 'Inv Metrics'[Total Store Stock Qty]", "Total Stock Qty": "\"Total Stock Qty\", 'Inv Metrics'[Total Stock Qty]", "Standard Cost": "\"Standard Cost\", 'Inv Metrics'[Standard Cost]", "Physical Stock Value @ Std Cost": "\"Physical Stock Value @ Std Cost\", 'Inv Metrics'[Physical Stock Value @ Std Cost]", "Store Stock In Transit Value @ Std Cost": "\"Store Stock In Transit Value @ Std Cost\", 'Inv Metrics'[Store Stock In Transit Value @ Std Cost]", "Store Stock On Hand Value @ Std Cost": "\"Store Stock On Hand Value @ Std Cost\", 'Inv Metrics'[Store Stock On Hand Value @ Std Cost]", "Total Store Stock Value @ Std Cost": "\"Total Store Stock Value @ Std Cost\", 'Inv Metrics'[Total Store Stock Value @ Std Cost]", "Total Stock Value @ Std Cost": "\"Total Stock Value @ Std Cost\", 'Inv Metrics'[Total Stock Value @ Std Cost]", "Retail Price[Latest]": "\"Retail Price[Latest]\", 'Inv Metrics'[Retail Price (Incl. VAT)]", "Physical Stock Value @ RRP (Incl. VAT)": "\"Physical Stock Value @ RRP (Incl. VAT)\", 'Inv Metrics'[Physical Stock Value @ RRP (Incl. VAT)]", "Physical Stock Value @ RRP (Excl. VAT)": "\"Physical Stock Value @ RRP (Excl. VAT)\", 'Inv Metrics'[Physical Stock Value @ RRP (Excl. VAT)]", "Store Stock In Transit Value @ RRP (Incl. VAT)": "\"Store Stock In Transit Value @ RRP (Incl. VAT)\", 'Inv Metrics'[Store Stock In Transit Value @ RRP (Incl. VAT)]", "Store Stock In Transit Value @ RRP (Excl. VAT)": "\"Store Stock In Transit Value @ RRP (Excl. VAT)\", 'Inv Metrics'[Store Stock In Transit Value @ RRP (Excl. VAT)]", "Store Stock On Hand Value @ RRP (Incl. VAT)": "\"Store Stock On Hand Value @ RRP (Incl. VAT)\", 'Inv Metrics'[Store Stock On Hand Value @ RRP (Incl. VAT)]", "Store Stock On Hand Value @ RRP (Excl. VAT)": "\"Store Stock On Hand Value @ RRP (Excl. VAT)\", 'Inv Metrics'[Store Stock On Hand Value @ RRP (Excl. VAT)]", "Total Store Stock Value @ RRP (Incl. VAT)": "\"Total Store Stock Value @ RRP (Incl. VAT)\", 'Inv Metrics'[Total Store Stock Value @ RRP (Incl. VAT)]", "Total Store Stock Value @ RRP (Excl. VAT)": "\"Total Store Stock Value @ RRP (Excl. VAT)\", 'Inv Metrics'[Total Store Stock Value @ RRP (Excl. VAT)]", "Total Stock Value @ RRP (Incl. VAT)": "\"Total Stock Value @ RRP (Incl. VAT)\", 'Inv Metrics'[Total Stock Value @ RRP (Incl. VAT)]", "Total Stock Value @ RRP (Excl. VAT)": "\"Total Stock Value @ RRP (Excl. VAT)\", 'Inv Metrics'[Total Stock Value @ RRP (Excl. VAT)]", "Total Stock Qty (for MOH)": "\"Total Stock Qty (for MOH)\", 'MOH Metrics'[Total Stock Qty (for MOH)]", "MOH (Monthend 3M)": "\"MOH (Monthend,3M)\", 'MOH Metrics'[MOH (Monthend 3Month)]", "TTL Net Qty (3M)": "\"TTL Net Qty (3M)\", 'MOH Metrics'[TTL Net Qty (3 Month)]", "Avg. Qty (3M)": "\"Avg. Qty (3M)\", 'MOH Metrics'[Avg 3 Month (QTY)]", "MOH (Monthend  12M)": "\"MOH (Monthend, 12M)\", 'MOH Metrics'[MOH (Monthend 12Month)]", "TTL Net Qty (12M)": "\"TTL Net Qty (12M)\", 'MOH Metrics'[TTL Net Qty (12 Month)]", "Avg. Qty (12M)": "\"Avg. Qty (12M)\", 'MOH Metrics'[Avg 12 Month (QTY)]", "Total Stock Value @ RRP (for MOH)": "\"Total Stock Value @ RRP (for MOH)\", 'MOH Metrics'[Total Stock Value @ RRP (for MOH)]", "MOH (Monthend  3M) (RPV)": "\"MOH (Monthend, 3M) (RPV)\", 'MOH Metrics'[MOH (Monthend 3Month) (RPV)]", "Total Gross Sales (3M)": "\"Total Gross Sales (3M)\", 'MOH Metrics'[Total Gross Sales (3 Month)]", "Avg. Gross Sales (3M)": "\"Avg. Gross Sales (3M)\", 'MOH Metrics'[Avg 3 months (Gross Sales)]", "MOH (Monthend  12M) (RPV)": "\"MOH (Monthend, 12M) (RPV)\", 'MOH Metrics'[MOH (Monthend 12Month) (RPV)]", "Total Gross Sales (12M)": "\"Total Gross Sales (12M)\", 'MOH Metrics'[Total Gross Sales (12 Month)]", "Avg. Gross Sales (12M)": "\"Avg. Gross Sales (12M)\", 'MOH Metrics'[Avg 12 months (Gross Sales)]", "No.of article (with inventory)": "\"No.of article (with inventory)\", 'MOH Metrics'[No.of article (with inventory)]"}