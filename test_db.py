import boto3
from boto3.dynamodb.conditions import Key

dynamodb = boto3.resource('dynamodb', region_name='cn-north-1')
table = dynamodb.Table('powerbi_downloads')

# 获取单个项目
response = table.get_item(Key={'id': 'fdf88542-df74-4378-940c-f1f63c21e7f9'})

# 检查响应中是否存在 'Item'
if 'Item' in response:
    item = response['Item']
    if 'conditions' in item:
        print(f"Conditions: {item['conditions']}")
    else:
        print("No conditions column found for this item")
else:
    print("Item not found in database")