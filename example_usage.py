#!/usr/bin/env python3
"""
PowerBI 下载器类使用示例
演示如何使用JSON格式调用download_data方法
"""

from pbi_downloader_class import PowerBIDownloader
import json


def example_inventory_download():
    """库存报表下载示例"""
    print("📦 库存报表下载示例")
    print("=" * 40)
    
    # 创建下载器实例
    downloader = PowerBIDownloader()
    
    # 库存报表的JSON参数
    json_data = {
        "report_name": "TW Inventory",
        "dim": "Snapshot Date,Article No,Global Store Code",
        "fcst": "Stock Qty,Stock Value",
        "Snapshot_date": ["2024/01/01", "2024/01/02", "2024/01/03"],
        "article_no": ["All"],
        "stock_category": ["All"],
        "global_store_code": ["All"],
        "store_type": ["All"],
        "stock_position": ["All"],
        "inv_owner": ["All"]
    }
    
    print("📋 请求参数:")
    print(json.dumps(json_data, indent=2, ensure_ascii=False))
    print()
    
    try:
        # 调用下载方法
        result = downloader.download_data(json_data)
        print(f"✅ 下载成功！结果: {result}")
        return True
        
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return False


def example_transaction_download():
    """交易报表下载示例"""
    print("\n💰 交易报表下载示例")
    print("=" * 40)
    
    # 创建下载器实例
    downloader = PowerBIDownloader()
    
    # 交易报表的JSON参数
    json_data = {
        "report_name": "TW Transaction Template",
        "dim": "DATE,Article No,Store Code",
        "fcst": "Sales Qty,Sales Value",
        "damin": "2024/01/01",
        "damax": "2024/01/07",
        "launch": ["All"],
        "pro_type": ["All"],
        "pro_div": ["All"],
        "pro_article": ["All"],
        "pro_category": ["All"],
        "pro_yeezy": ["All"],
        "sto_code": ["All"],
        "sto_status": ["All"],
        "sto_main_channel": ["All"],
        "sto_format": ["All"],
        "sto_type": ["All"]
    }
    
    print("📋 请求参数:")
    print(json.dumps(json_data, indent=2, ensure_ascii=False))
    print()
    
    try:
        # 调用下载方法
        result = downloader.download_data(json_data)
        print(f"✅ 下载成功！结果: {result}")
        return True
        
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return False


def example_with_filters():
    """带过滤条件的下载示例"""
    print("\n🔍 带过滤条件的下载示例")
    print("=" * 40)
    
    # 创建下载器实例
    downloader = PowerBIDownloader()
    
    # 带具体过滤条件的JSON参数
    json_data = {
        "report_name": "TW Inventory",
        "dim": "Snapshot Date,Article No,Global Store Code,Stock Category",
        "fcst": "Stock Qty,Stock Value",
        "Snapshot_date": ["2024/01/01"],
        "article_no": ["FX3624", "GZ2864", "HQ2200"],  # 具体的货号
        "stock_category": ["FOOTWEAR"],                 # 具体的品类
        "global_store_code": ["TW001", "TW002"],       # 具体的店铺
        "store_type": ["RETAIL"],                       # 店铺类型
        "stock_position": ["SELLABLE"],                 # 库存状态
        "inv_owner": ["ADIDAS"]                         # 库存所有者
    }
    
    print("📋 请求参数 (带过滤条件):")
    print(json.dumps(json_data, indent=2, ensure_ascii=False))
    print()
    
    try:
        # 调用下载方法
        result = downloader.download_data(json_data)
        print(f"✅ 下载成功！结果: {result}")
        return True
        
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return False


def example_json_string():
    """使用JSON字符串的示例"""
    print("\n📝 JSON字符串格式示例")
    print("=" * 40)
    
    # 创建下载器实例
    downloader = PowerBIDownloader()
    
    # JSON字符串格式
    json_string = '''
    {
        "report_name": "TW Inventory",
        "dim": "Snapshot Date,Article No",
        "fcst": "Stock Qty",
        "Snapshot_date": ["2024/01/01"],
        "article_no": ["All"],
        "stock_category": ["All"]
    }
    '''
    
    print("📋 JSON字符串:")
    print(json_string)
    
    try:
        # 直接传入JSON字符串
        result = downloader.download_data(json_string)
        print(f"✅ 下载成功！结果: {result}")
        return True
        
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return False


def simulate_api_request():
    """模拟API请求的示例"""
    print("\n🌐 模拟API请求示例")
    print("=" * 40)
    
    # 模拟从API接收到的参数（类似FastAPI的Query参数）
    api_params = {
        "reprot_name": "TW Inventory",  # 注意：这里故意保持原API中的拼写错误
        "user_name": "<EMAIL>",
        "dim": "Snapshot Date,Article No,Global Store Code",
        "fcst": "Stock Qty,Stock Value",
        "Snapshot_date": '["2024/01/01", "2024/01/02"]',  # 字符串格式的JSON数组
        "article_no": '["All"]',
        "stock_category": '["FOOTWEAR"]',
        "global_store_code": '["All"]'
    }
    
    # 转换为下载器需要的格式
    json_data = {
        "report_name": api_params["reprot_name"],  # 修正字段名
        "dim": api_params["dim"],
        "fcst": api_params["fcst"],
        "Snapshot_date": json.loads(api_params["Snapshot_date"]),
        "article_no": json.loads(api_params["article_no"]),
        "stock_category": json.loads(api_params["stock_category"]),
        "global_store_code": json.loads(api_params["global_store_code"])
    }
    
    print("📋 API参数:")
    print(json.dumps(api_params, indent=2, ensure_ascii=False))
    print("\n📋 转换后的JSON数据:")
    print(json.dumps(json_data, indent=2, ensure_ascii=False))
    print()
    
    try:
        # 创建下载器并调用
        downloader = PowerBIDownloader()
        result = downloader.download_data(json_data)
        print(f"✅ 下载成功！结果: {result}")
        return True
        
    except Exception as e:
        print(f"❌ 下载失败: {e}")
        return False


def main():
    """主函数 - 运行所有示例"""
    print("🚀 PowerBI 下载器使用示例")
    print("=" * 60)
    
    examples = [
        ("库存报表下载", example_inventory_download),
        ("交易报表下载", example_transaction_download),
        ("带过滤条件下载", example_with_filters),
        ("JSON字符串格式", example_json_string),
        ("模拟API请求", simulate_api_request)
    ]
    
    success_count = 0
    total_count = len(examples)
    
    for name, example_func in examples:
        try:
            print(f"\n🧪 运行示例: {name}")
            if example_func():
                success_count += 1
                print(f"✅ {name} 示例完成")
            else:
                print(f"❌ {name} 示例失败")
        except Exception as e:
            print(f"❌ {name} 示例异常: {e}")
    
    print("\n" + "=" * 60)
    print("📊 示例运行结果:")
    print(f"总示例数: {total_count}")
    print(f"成功数: {success_count}")
    print(f"失败数: {total_count - success_count}")
    
    if success_count == total_count:
        print("🎉 所有示例都运行成功！")
    else:
        print("⚠️ 部分示例失败（可能需要实际的PowerBI连接）")
    
    print("\n💡 注意:")
    print("- 这些示例展示了如何使用JSON格式调用download_data方法")
    print("- 实际运行需要有效的PowerBI连接和配置文件")
    print("- 可以根据需要修改参数和过滤条件")


if __name__ == "__main__":
    main()
