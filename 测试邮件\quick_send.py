"""
快速发送邮件脚本
简单易用的邮件发送工具
"""

from outlook_email_sender import OutlookEmailSender

def quick_send_text_email(to_email, subject, message):
    """快速发送文本邮件"""
    sender = OutlookEmailSender()
    
    # 你的邮箱配置
    sender_email = "<EMAIL>"
    sender_password = "LyfJason!999"
    
    success = sender.send_email_smtp(
        sender_email=sender_email,
        sender_password=sender_password,
        recipient_emails=[to_email],
        subject=subject,
        body=message
    )
    
    return success

def quick_send_html_email(to_email, subject, html_message):
    """快速发送HTML邮件"""
    sender = OutlookEmailSender()
    
    # 你的邮箱配置
    sender_email = "<EMAIL>"
    sender_password = "LyfJason!999"
    
    success = sender.send_email_smtp(
        sender_email=sender_email,
        sender_password=sender_password,
        recipient_emails=[to_email],
        subject=subject,
        body=html_message,
        is_html=True
    )
    
    return success

def send_notification_email():
    """发送通知邮件示例"""
    html_content = """
    <html>
        <body style="font-family: Arial, sans-serif;">
            <h2 style="color: #0078d4;">📧 系统通知</h2>
            <p>这是一封自动发送的通知邮件。</p>
            <div style="background: #f0f8ff; padding: 15px; border-left: 4px solid #0078d4;">
                <strong>重要提醒：</strong><br>
                您的 Python 邮件发送功能已经配置完成并正常工作！
            </div>
            <p>如有任何问题，请及时联系技术支持。</p>
            <hr>
            <small style="color: #666;">此邮件由系统自动发送，请勿回复。</small>
        </body>
    </html>
    """
    
    success = quick_send_html_email(
        to_email="<EMAIL>",
        subject="🔔 系统通知 - Python 邮件功能测试",
        html_message=html_content
    )
    
    if success:
        print("✅ 通知邮件发送成功！")
    else:
        print("❌ 通知邮件发送失败！")

def send_report_email():
    """发送报告邮件示例"""
    html_content = """
    <html>
        <head>
            <style>
                table { border-collapse: collapse; width: 100%; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #0078d4; color: white; }
                .success { color: #28a745; font-weight: bold; }
                .info { background: #e3f2fd; padding: 10px; border-radius: 5px; }
            </style>
        </head>
        <body style="font-family: Arial, sans-serif;">
            <h2>📊 邮件发送测试报告</h2>
            
            <div class="info">
                <strong>测试时间：</strong> 2024年7月31日<br>
                <strong>测试状态：</strong> <span class="success">✅ 成功</span>
            </div>
            
            <h3>测试结果详情：</h3>
            <table>
                <tr>
                    <th>测试项目</th>
                    <th>状态</th>
                    <th>说明</th>
                </tr>
                <tr>
                    <td>文本邮件发送</td>
                    <td class="success">✅ 成功</td>
                    <td>基础文本邮件发送正常</td>
                </tr>
                <tr>
                    <td>HTML邮件发送</td>
                    <td class="success">✅ 成功</td>
                    <td>支持富文本格式和样式</td>
                </tr>
                <tr>
                    <td>抄送功能</td>
                    <td class="success">✅ 成功</td>
                    <td>CC功能正常工作</td>
                </tr>
            </table>
            
            <h3>技术配置：</h3>
            <ul>
                <li>SMTP服务器：smtp-mail.outlook.com</li>
                <li>端口：587 (TLS加密)</li>
                <li>认证方式：用户名密码</li>
                <li>编码：UTF-8</li>
            </ul>
            
            <p><strong>结论：</strong> 所有邮件发送功能测试通过，系统运行正常。</p>
        </body>
    </html>
    """
    
    success = quick_send_html_email(
        to_email="<EMAIL>",
        subject="📈 邮件系统测试报告",
        html_message=html_content
    )
    
    if success:
        print("✅ 报告邮件发送成功！")
    else:
        print("❌ 报告邮件发送失败！")

if __name__ == "__main__":
    print("🚀 快速邮件发送工具")
    print("=" * 30)
    
    # 示例1：发送通知邮件
    print("1. 发送通知邮件...")
    send_notification_email()
    
    print("\n2. 发送报告邮件...")
    send_report_email()
    
    print("\n" + "=" * 30)
    print("📧 邮件发送完成！请检查收件箱。")
    
    # 你也可以直接调用函数发送自定义邮件：
    # quick_send_text_email("<EMAIL>", "测试主题", "测试内容")
    # quick_send_html_email("<EMAIL>", "HTML主题", "<h1>HTML内容</h1>")
