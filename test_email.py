from send_email import send_custom_email

def test_send_email():

    # 邮件参数
    to_email = "<EMAIL>"  # 收件人
    subject = "报告下载成功"  # 邮件主题
    message = """
                亲爱的用户，
                
                您的报告已经成功下载并处理完成。
                
                详细信息：
                - 处理时间：2024年7月31日
                - 状态：成功
                - 文件类型：数据报告
                
                如有任何问题，请及时联系。
                
                祝好！
                系统自动发送
                    """.strip()

    success = send_custom_email(to_email, subject, message)

    if success:
        print("✅ 邮件发送成功！")
    else:
        print("❌ 邮件发送失败！")

    return success

def main():
    test_send_email()

if __name__ == "__main__":
    main()