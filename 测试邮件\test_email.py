"""
测试邮件发送脚本
使用提供的邮箱账号发送测试邮件
"""

from outlook_email_sender import OutlookEmailSender

def send_simple_email():
    """发送简单文本邮件"""
    print("=== 发送简单文本邮件 ===")
    
    sender = OutlookEmailSender()
    
    # 使用提供的邮箱信息
    sender_email = "<EMAIL>"
    sender_password = "LyfJason!999"
    recipient_emails = ["<EMAIL>"]  # 发送给自己测试
    
    success = sender.send_email_smtp(
        sender_email=sender_email,
        sender_password=sender_password,
        recipient_emails=recipient_emails,
        subject="Python 测试邮件 - 简单文本",
        body="这是一封通过 Python 发送的简单测试邮件！\n\n发送时间：2024年\n\n祝好！"
    )
    
    if success:
        print("✅ 简单邮件发送成功！")
    else:
        print("❌ 简单邮件发送失败！")
    
    return success

def send_html_email():
    """发送 HTML 格式邮件"""
    print("\n=== 发送 HTML 格式邮件 ===")
    
    sender = OutlookEmailSender()
    
    # 使用提供的邮箱信息
    sender_email = "<EMAIL>"
    sender_password = "LyfJason!999"
    recipient_emails = ["<EMAIL>"]  # 发送给自己测试
    
    # 精美的 HTML 邮件内容
    html_content = """
    <html>
        <head>
            <style>
                body { 
                    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
                    line-height: 1.6;
                    color: #333;
                    max-width: 600px;
                    margin: 0 auto;
                    padding: 20px;
                }
                .header { 
                    background: linear-gradient(135deg, #0078d4, #106ebe);
                    color: white; 
                    padding: 30px;
                    text-align: center;
                    border-radius: 10px 10px 0 0;
                }
                .header h1 {
                    margin: 0;
                    font-size: 28px;
                }
                .content { 
                    background: #f8f9fa;
                    padding: 30px;
                    border-radius: 0 0 10px 10px;
                    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
                }
                .highlight {
                    background: #e3f2fd;
                    padding: 15px;
                    border-left: 4px solid #0078d4;
                    margin: 20px 0;
                }
                .feature-list {
                    background: white;
                    padding: 20px;
                    border-radius: 8px;
                    margin: 20px 0;
                }
                .feature-list li {
                    margin: 10px 0;
                    padding-left: 10px;
                }
                .footer { 
                    color: #666; 
                    font-size: 14px;
                    text-align: center;
                    margin-top: 30px;
                    padding-top: 20px;
                    border-top: 1px solid #ddd;
                }
                .button {
                    display: inline-block;
                    background: #0078d4;
                    color: white;
                    padding: 12px 24px;
                    text-decoration: none;
                    border-radius: 6px;
                    margin: 20px 0;
                }
                .success {
                    color: #28a745;
                    font-weight: bold;
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>🎉 Python 邮件发送测试</h1>
                <p>HTML 格式邮件演示</p>
            </div>
            
            <div class="content">
                <h2>亲爱的用户，</h2>
                
                <p>恭喜！这是一封通过 <strong>Python</strong> 成功发送的 <em>HTML 格式</em> 邮件。</p>
                
                <div class="highlight">
                    <h3>✨ 测试成功！</h3>
                    <p class="success">您的 Outlook 365 邮件发送功能已经正常工作。</p>
                </div>
                
                <div class="feature-list">
                    <h3>📋 支持的功能：</h3>
                    <ul>
                        <li>✅ 发送纯文本邮件</li>
                        <li>✅ 发送 HTML 格式邮件</li>
                        <li>✅ 添加附件</li>
                        <li>✅ 抄送和密送</li>
                        <li>✅ 批量发送</li>
                        <li>✅ 自定义样式</li>
                    </ul>
                </div>
                
                <p>访问官方文档了解更多：</p>
                <a href="https://docs.microsoft.com/zh-cn/outlook/" class="button">📖 查看文档</a>
                
                <div class="highlight">
                    <p><strong>技术信息：</strong></p>
                    <ul>
                        <li>发送方式：SMTP</li>
                        <li>服务器：smtp-mail.outlook.com</li>
                        <li>端口：587 (TLS)</li>
                        <li>编码：UTF-8</li>
                    </ul>
                </div>
            </div>
            
            <div class="footer">
                <p>此邮件由 Python 自动发送 | 测试时间：2024年</p>
                <p>如有问题，请检查网络连接和邮箱设置</p>
            </div>
        </body>
    </html>
    """
    
    success = sender.send_email_smtp(
        sender_email=sender_email,
        sender_password=sender_password,
        recipient_emails=recipient_emails,
        subject="🎨 Python HTML 邮件测试 - 精美格式",
        body=html_content,
        is_html=True
    )
    
    if success:
        print("✅ HTML 邮件发送成功！")
    else:
        print("❌ HTML 邮件发送失败！")
    
    return success

def send_email_with_cc():
    """发送带抄送的邮件"""
    print("\n=== 发送带抄送的邮件 ===")
    
    sender = OutlookEmailSender()
    
    sender_email = "<EMAIL>"
    sender_password = "LyfJason!999"
    recipient_emails = ["<EMAIL>"]
    
    success = sender.send_email_smtp(
        sender_email=sender_email,
        sender_password=sender_password,
        recipient_emails=recipient_emails,
        subject="📧 Python 邮件测试 - 带抄送功能",
        body="这是一封测试抄送功能的邮件。\n\n主要收件人和抄送人都会收到这封邮件。",
        cc_emails=["<EMAIL>"]  # 抄送给自己
    )
    
    if success:
        print("✅ 带抄送的邮件发送成功！")
    else:
        print("❌ 带抄送的邮件发送失败！")
    
    return success

def main():
    """主函数 - 运行所有测试"""
    print("🚀 开始测试 Outlook 365 邮件发送功能")
    print("=" * 50)
    
    # 测试计数
    total_tests = 0
    passed_tests = 0
    
    # 测试1：简单文本邮件
    total_tests += 1
    if send_simple_email():
        passed_tests += 1
    
    # 测试2：HTML 邮件
    total_tests += 1
    if send_html_email():
        passed_tests += 1
    
    # 测试3：带抄送的邮件
    total_tests += 1
    if send_email_with_cc():
        passed_tests += 1
    
    # 显示测试结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总")
    print(f"总测试数：{total_tests}")
    print(f"成功数：{passed_tests}")
    print(f"失败数：{total_tests - passed_tests}")
    
    if passed_tests == total_tests:
        print("🎉 所有测试都通过了！邮件发送功能正常工作。")
    else:
        print("⚠️  部分测试失败，请检查网络连接和邮箱设置。")
    
    print("\n💡 提示：")
    print("- 如果认证失败，可能需要启用'应用密码'")
    print("- 检查邮箱的安全设置")
    print("- 确保网络连接正常")

if __name__ == "__main__":
    main()
