#!/usr/bin/env python3
"""
PowerBI 下载器类使用示例
演示如何使用 PowerBIDownloader 类
"""

from pbi_downloader_class import PowerBIDownloader
import json


def example_basic_usage():
    """基本使用示例"""
    print("📦 基本使用示例")
    print("=" * 40)
    
    # 创建下载器实例
    downloader = PowerBIDownloader()
    
    # 准备JSON数据
    json_data = {
        "report_name": "TW Inventory",
        "dim": "Snapshot Date,Article No",
        "fcst": "Total Stock Qty",
        "Snapshot_date": ["2024/01/01", "2024/01/02"],
        "article_no": ["All"],
        "stock_category": ["All"]
    }
    
    print("📋 请求参数:")
    print(json.dumps(json_data, indent=2, ensure_ascii=False))
    
    try:
        # 调用下载方法
        result = downloader.download_data(json_data)
        print(f"✅ 下载成功: {result}")
        
    except Exception as e:
        print(f"❌ 下载失败: {e}")


def example_with_custom_config():
    """自定义配置示例"""
    print("\n⚙️ 自定义配置示例")
    print("=" * 40)
    
    # 使用自定义配置创建下载器
    downloader = PowerBIDownloader(
        client_id="your-custom-client-id",
        workspace_name="your-custom-workspace",
        base_dir="your-custom-config-path"
    )
    
    print(f"📁 自定义下载目录: {downloader.custom_dir}")
    print(f"🏢 自定义工作区: {downloader.workspace_name}")


def example_transaction_report():
    """交易报表示例"""
    print("\n💰 交易报表示例")
    print("=" * 40)
    
    downloader = PowerBIDownloader()
    
    json_data = {
        "report_name": "TW Transaction Template",
        "dim": "DATE,Article No,Store Code",
        "fcst": "Sales Qty,Sales Value",
        "damin": "2024/01/01",
        "damax": "2024/01/07",
        "pro_type": ["FOOTWEAR"],
        "sto_main_channel": ["RETAIL"]
    }
    
    print("📋 交易报表参数:")
    print(json.dumps(json_data, indent=2, ensure_ascii=False))
    
    try:
        result = downloader.download_data(json_data)
        print(f"✅ 交易报表下载成功: {result}")
        
    except Exception as e:
        print(f"❌ 交易报表下载失败: {e}")


def example_with_filters():
    """带过滤条件示例"""
    print("\n🔍 带过滤条件示例")
    print("=" * 40)
    
    downloader = PowerBIDownloader()
    
    json_data = {
        "report_name": "TW Inventory",
        "dim": "Snapshot Date,Article No,Global Store Code,Stock Category",
        "fcst": "Total Stock Qty,Stock Value",
        "Snapshot_date": ["2024/01/01"],
        "article_no": ["FX3624", "GZ2864", "HQ2200"],  # 具体货号
        "stock_category": ["FOOTWEAR"],                 # 鞋类
        "global_store_code": ["TW001", "TW002"],       # 特定店铺
        "store_type": ["RETAIL"],                       # 零售店
        "stock_position": ["SELLABLE"],                 # 可售库存
        "inv_owner": ["ADIDAS"]                         # 库存所有者
    }
    
    print("📋 带过滤条件的参数:")
    print(json.dumps(json_data, indent=2, ensure_ascii=False))
    
    try:
        result = downloader.download_data(json_data)
        print(f"✅ 过滤下载成功: {result}")
        
    except Exception as e:
        print(f"❌ 过滤下载失败: {e}")


def example_json_string():
    """JSON字符串示例"""
    print("\n📝 JSON字符串示例")
    print("=" * 40)
    
    downloader = PowerBIDownloader()
    
    # 直接使用JSON字符串
    json_string = '''
    {
        "report_name": "TW Inventory",
        "dim": "Snapshot Date,Article No",
        "fcst": "Total Stock Qty",
        "Snapshot_date": ["2024/01/01"],
        "article_no": ["All"],
        "stock_category": ["All"]
    }
    '''
    
    print("📋 JSON字符串:")
    print(json_string)
    
    try:
        result = downloader.download_data(json_string)
        print(f"✅ JSON字符串下载成功: {result}")
        
    except Exception as e:
        print(f"❌ JSON字符串下载失败: {e}")


def example_method_testing():
    """方法测试示例"""
    print("\n🧪 方法测试示例")
    print("=" * 40)
    
    downloader = PowerBIDownloader()
    
    # 测试字符串解析
    print("📋 测试字符串解析:")
    test_values = [
        "All",
        '["A", "B", "C"]',
        ["X", "Y"],
        None,
        "single_value"
    ]
    
    for value in test_values:
        result = downloader.parse_str_to_list(value)
        print(f"   {value} → {result}")
    
    # 测试季度计算
    print("\n📋 测试季度计算:")
    from datetime import datetime
    test_date = datetime(2024, 7, 15)
    quarter = downloader.get_quarter(test_date)
    print(f"   日期 {test_date.strftime('%Y-%m-%d')} 属于第 {quarter} 季度")
    
    # 测试季度范围
    start_date, end_date = downloader.get_quarter_start_end(2024, 2)
    print(f"   2024年第2季度: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")


def main():
    """主函数"""
    print("🚀 PowerBI 下载器类使用示例")
    print("=" * 60)
    
    # 运行所有示例
    examples = [
        example_basic_usage,
        example_with_custom_config,
        example_transaction_report,
        example_with_filters,
        example_json_string,
        example_method_testing
    ]
    
    for example in examples:
        try:
            example()
        except Exception as e:
            print(f"❌ 示例执行异常: {e}")
    
    print("\n" + "=" * 60)
    print("📋 使用说明:")
    print("1. 创建 PowerBIDownloader 实例")
    print("2. 准备 JSON 格式的参数")
    print("3. 调用 download_data() 方法")
    print("4. 处理返回结果")
    
    print("\n💡 注意事项:")
    print("- 确保配置文件路径正确")
    print("- 需要有效的 PowerBI 连接权限")
    print("- JSON 参数格式要正确")
    print("- 类保持了原有脚本的所有逻辑")
    
    print("\n🔧 类的主要优势:")
    print("- 面向对象设计，更好的代码组织")
    print("- 可复用的实例，支持多次调用")
    print("- 保持原有逻辑不变，确保兼容性")
    print("- 支持自定义配置和参数")


if __name__ == "__main__":
    main()
