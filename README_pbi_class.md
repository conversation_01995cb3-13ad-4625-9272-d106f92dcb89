# PowerBI 下载器类使用说明

## 概述

将原有的 `pbi_download_new.py` 函数式代码重构为面向对象的类结构，提供更好的代码组织和复用性。

## 文件结构

```
├── pbi_downloader_class.py    # PowerBI下载器类
├── test_pbi_downloader.py     # 测试脚本
└── README_pbi_class.md        # 使用说明
```

## 主要改进

### 1. 面向对象设计
- 将所有功能封装在 `PowerBIDownloader` 类中
- 更好的代码组织和维护性
- 支持多实例和配置复用

### 2. 简化的接口
- 主要方法：`download_data()` 统一入口
- 自动处理不同报表类型的差异
- 智能参数解析和验证

### 3. 模块化功能
- 独立的映射加载方法
- 可复用的日期计算功能
- 灵活的过滤器构建

## 使用方法

### 基本用法

```python
from pbi_downloader_class import PowerBIDownloader

# 创建下载器实例
downloader = PowerBIDownloader()

# 下载库存报表
file_path, rows, cols, size_mb, time_str = downloader.download_data(
    report_name="TW Inventory",
    dim_params="Snapshot Date,Article No,Global Store Code",
    fcst_params="Stock Qty,Stock Value",
    snapshot_dates=["2024/01/01", "2024/01/02"],
    article_no=["A12345", "B67890"],
    stock_category="FOOTWEAR"
)

# 下载交易报表
file_path, rows, cols, size_mb, time_str = downloader.download_data(
    report_name="TW Transaction Template",
    dim_params="DATE,Article No,Store Code",
    fcst_params="Sales Qty,Sales Value",
    date_min="2024/01/01",
    date_max="2024/01/07",
    pro_type="FOOTWEAR",
    sto_main_channel="RETAIL"
)
```

### 自定义配置

```python
# 使用自定义配置创建下载器
downloader = PowerBIDownloader(
    client_id="your-client-id",
    workspace_name="your-workspace",
    base_dir="/path/to/config/files"
)
```

### 测试功能

```python
# 运行测试脚本
python test_pbi_downloader.py
```

## 类方法说明

### 核心方法

#### `download_data()`
主要的数据下载方法，统一处理不同类型的报表下载。

**参数：**
- `report_name`: 报表名称 ("TW Inventory" 或 "TW Transaction Template")
- `dim_params`: 维度参数，逗号分隔的字符串
- `fcst_params`: 指标参数，逗号分隔的字符串
- `date_min/date_max`: 日期范围 (交易报表用)
- `snapshot_dates`: 快照日期列表 (库存报表用)
- `**filter_params`: 其他过滤参数

**返回：**
- `tuple`: (文件路径, 行数, 列数, 文件大小MB, 耗时)

### 工具方法

#### `load_all_mappings(name)`
加载指定报表的所有映射文件。

#### `build_dim_actual_names(dim_mapping)`
构建维度字段的实际名称映射。

#### `build_fcst_actual_names(fcst_mapping)`
构建指标字段的实际名称映射。

#### `build_dax_filter(filter_mapping, **kwargs)`
构建DAX查询的过滤条件。

#### `calculate_and_configure_batch(batch_type, start_date, end_date)`
计算时间跨度并配置优化参数。

## 配置要求

### 必需的配置文件
确保以下文件存在于 `base_dir` 目录中：

```
├── practical_config.json
├── dim_mapping_inventory.json
├── fcst_mapping_inventory.json
├── filter_mapping_inventory.json
├── dim_mapping_transaction.json
├── fcst_mapping_transaction.json
└── filter_mapping_transaction.json
```

### Azure AD 配置
- 有效的 `client_id`、`client_secret`、`tenant_id`
- PowerBI 工作区访问权限
- 必要的 API 权限

## 测试说明

测试脚本 `test_pbi_downloader.py` 包含以下测试：

1. **映射功能测试** - 验证配置文件加载和映射构建
2. **日期计算功能测试** - 验证季度计算和批次配置
3. **工具函数测试** - 验证字符串解析和工具方法
4. **下载功能测试** - 验证实际数据下载（需要有效连接）

运行测试：
```bash
python test_pbi_downloader.py
```

## 注意事项

1. **依赖项**：确保安装所有必需的Python包
2. **权限**：需要有效的PowerBI和Azure AD权限
3. **配置**：确保所有配置文件路径正确
4. **网络**：需要稳定的网络连接

## 错误处理

类中包含完整的错误处理机制：
- 参数验证
- 文件路径检查
- 网络连接异常处理
- 详细的错误日志

## 扩展性

类设计支持：
- 添加新的报表类型
- 自定义批次策略
- 扩展过滤条件
- 集成其他数据源

## 性能优化

- 智能批次分割
- 并发下载支持
- 内存优化处理
- 临时文件自动清理
