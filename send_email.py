import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import List, Optional

# 邮箱配置
sender_email = "<EMAIL>"
sender_password = "LyfJason!999"

class SimpleEmailSender:
    def __init__(self):
        self.smtp_server = "smtp-mail.outlook.com"
        self.smtp_port = 587
        
    def send_email(self, 
                   sender_email: str,
                   sender_password: str,
                   recipient_emails: List[str],
                   subject: str,
                   body: str,
                   cc_emails: Optional[List[str]] = None):
        try:
            # 创建邮件对象
            message = MIMEMultipart()
            message["From"] = sender_email
            message["To"] = ", ".join(recipient_emails)
            message["Subject"] = subject
            
            if cc_emails:
                message["Cc"] = ", ".join(cc_emails)
            
            # 添加邮件正文（纯文本）
            message.attach(MIMEText(body, "plain", "utf-8"))
            
            # 准备收件人列表
            all_recipients = recipient_emails.copy()
            if cc_emails:
                all_recipients.extend(cc_emails)
            
            # 创建安全连接并发送邮件
            context = ssl.create_default_context()
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls(context=context)
                server.login(sender_email, sender_password)
                server.sendmail(sender_email, all_recipients, message.as_string())
            
            print(f"✅ 邮件发送成功！收件人: {', '.join(recipient_emails)}")
            return True
            
        except Exception as e:
            print(f"❌ 邮件发送失败: {str(e)}")
            return False

def send_custom_email(to_email, subject, message):
    """发送自定义邮件"""
    sender = SimpleEmailSender()



    print(f"📧 准备发送邮件:")
    print(f"   收件人: {to_email}")
    print(f"   主题: {subject}")

    success = sender.send_email(
        sender_email=sender_email,
        sender_password=sender_password,
        recipient_emails=[to_email],
        subject=subject,
        body=message
    )

    return success


def main():

    # 发送测试邮件
    success = send_custom_email("<EMAIL>", "测试邮件", "这是一封测试邮件！")
    
    if success:
        print("邮件发送功能正常工作！")
    else:
        print("邮件发送失败，请检查配置。")
    
if __name__ == "__main__":
    main()

