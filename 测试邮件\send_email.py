import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from typing import List, Optional

# 邮箱配置
sender_email = "<EMAIL>"
sender_password = "LyfJason!999"

class SimpleEmailSender:
    def __init__(self):
        self.smtp_server = "smtp-mail.outlook.com"
        self.smtp_port = 587
        
    def send_email(self,
                   sender_email: str,
                   sender_password: str,
                   recipient_emails: List[str],
                   subject: str,
                   body: str,
                   cc_emails: Optional[List[str]] = None,
                   is_html: bool = False):
        try:
            # 创建邮件对象
            message = MIMEMultipart()
            message["From"] = sender_email
            message["To"] = ", ".join(recipient_emails)
            message["Subject"] = subject
            
            if cc_emails:
                message["Cc"] = ", ".join(cc_emails)
            
            # 添加邮件正文（根据 is_html 参数选择格式）
            if is_html:
                message.attach(MIMEText(body, "html", "utf-8"))
            else:
                message.attach(MIMEText(body, "plain", "utf-8"))
            
            # 准备收件人列表
            all_recipients = recipient_emails.copy()
            if cc_emails:
                all_recipients.extend(cc_emails)
            
            # 创建安全连接并发送邮件
            context = ssl.create_default_context()
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                server.starttls(context=context)
                server.login(sender_email, sender_password)
                server.sendmail(sender_email, all_recipients, message.as_string())
            
            print(f"✅ 邮件发送成功！收件人: {', '.join(recipient_emails)}")
            return True
            
        except Exception as e:
            print(f"❌ 邮件发送失败: {str(e)}")
            return False

def send_custom_email(to_email, subject, message, is_html):
    """发送自定义邮件"""
    sender = SimpleEmailSender()



    print(f"📧 准备发送邮件:")
    print(f"   收件人: {to_email}")
    print(f"   主题: {subject}")
    print(f"   格式: {'HTML' if is_html else '纯文本'}")

    success = sender.send_email(
        sender_email=sender_email,
        sender_password=sender_password,
        recipient_emails=[to_email],
        subject=subject,
        body=message,
        is_html=is_html
    )

    return success

